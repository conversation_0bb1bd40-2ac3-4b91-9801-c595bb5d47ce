---@module "luassert"

local gitbrowse = require("snacks.gitbrowse")

-- stylua: ignore
local git_remotes_cases = {
  ["https://github.com/LazyVim/LazyVim.git"]                             = "https://github.com/LazyVim/LazyVim",
  ["https://github.com/LazyVim/LazyVim"]                                 = "https://github.com/LazyVim/LazyVim",
  ["**************:LazyVim/LazyVim"]                                     = "https://github.com/LazyVim/LazyVim",
  ["*********************:v3/neovim-org/owner/repo"]                     = "https://dev.azure.com/neovim-org/owner/_git/repo",
  ["https://<EMAIL>/samiulazim/neovim.git"]          = "https://bitbucket.org/samiulazim/neovim",
  ["*****************:samiulazim/neovim.git"]                            = "https://bitbucket.org/samiulazim/neovim",
  ["**************:inkscape/inkscape.git"]                               = "https://gitlab.com/inkscape/inkscape",
  ["https://gitlab.com/inkscape/inkscape.git"]                           = "https://gitlab.com/inkscape/inkscape",
  ["**************:torvalds/linux.git"]                                  = "https://github.com/torvalds/linux",
  ["https://github.com/torvalds/linux.git"]                              = "https://github.com/torvalds/linux",
  ["*****************:team/repo.git"]                                    = "https://bitbucket.org/team/repo",
  ["https://bitbucket.org/team/repo.git"]                                = "https://bitbucket.org/team/repo",
  ["**************:example-group/example-project.git"]                   = "https://gitlab.com/example-group/example-project",
  ["https://gitlab.com/example-group/example-project.git"]               = "https://gitlab.com/example-group/example-project",
  ["*********************:v3/org/project/repo"]                          = "https://dev.azure.com/org/project/_git/repo",
  ["https://<EMAIL>/org/project/_git/repo"]               = "https://dev.azure.com/org/project/_git/repo",
  ["ssh://*******************:2222/org/repo.git"]                        = "https://ghe.example.com/org/repo",
  ["https://ghe.example.com/org/repo.git"]                               = "https://ghe.example.com/org/repo",
  ["git-codecommit.us-east-1.amazonaws.com/v1/repos/MyDemoRepo"]         = "https://git-codecommit.us-east-1.amazonaws.com/v1/repos/MyDemoRepo",
  ["https://git-codecommit.us-east-1.amazonaws.com/v1/repos/MyDemoRepo"] = "https://git-codecommit.us-east-1.amazonaws.com/v1/repos/MyDemoRepo",
  ["ssh://********************************:2022/p/project/r/repo"]       = "https://source.developers.google.com/p/project/r/repo",
  ["https://source.developers.google.com/p/project/r/repo"]              = "https://source.developers.google.com/p/project/r/repo",
  ["*************:~user/repo"]                                           = "https://git.sr.ht/~user/repo",
  ["https://git.sr.ht/~user/repo"]                                       = "https://git.sr.ht/~user/repo",
  ["*************:~user/another-repo"]                                   = "https://git.sr.ht/~user/another-repo",
  ["https://git.sr.ht/~user/another-repo"]                               = "https://git.sr.ht/~user/another-repo",
}

describe("util.lazygit", function()
  for remote, expected in pairs(git_remotes_cases) do
    it("should parse git remote " .. remote, function()
      local url = gitbrowse.get_repo(remote)
      assert.are.equal(expected, url)
    end)
  end
end)
