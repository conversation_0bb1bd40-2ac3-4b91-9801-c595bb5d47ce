*snacks-rename*                                              snacks_rename

==============================================================================
Table of Contents                            *snacks-rename-table-of-contents*

1. Usage                                                 |snacks-rename-usage|
2. mini.files                                       |snacks-rename-mini.files|
3. oil.nvim                                           |snacks-rename-oil.nvim|
4. neo-tree.nvim                                 |snacks-rename-neo-tree.nvim|
5. nvim-tree                                         |snacks-rename-nvim-tree|
6. Module                                               |snacks-rename-module|
  - Snacks.rename.on_rename_file()|snacks-rename-module-snacks.rename.on_rename_file()|
  - Snacks.rename.rename_file()|snacks-rename-module-snacks.rename.rename_file()|
LSP-integrated file renaming with support for plugins like neo-tree.nvim
<https://github.com/nvim-neo-tree/neo-tree.nvim> and mini.files
<https://github.com/echas<PERSON>ski/mini.files>.


==============================================================================
1. Usage                                                 *snacks-rename-usage*


==============================================================================
2. mini.files                                       *snacks-rename-mini.files*

>lua
    vim.api.nvim_create_autocmd("User", {
      pattern = "MiniFilesActionRename",
      callback = function(event)
        Snacks.rename.on_rename_file(event.data.from, event.data.to)
      end,
    })
<


==============================================================================
3. oil.nvim                                           *snacks-rename-oil.nvim*

>lua
    vim.api.nvim_create_autocmd("User", {
      pattern = "OilActionsPost",
      callback = function(event)
          if event.data.actions.type == "move" then
              Snacks.rename.on_rename_file(event.data.actions.src_url, event.data.actions.dest_url)
          end
      end,
    })
<


==============================================================================
4. neo-tree.nvim                                 *snacks-rename-neo-tree.nvim*

>lua
    {
      "nvim-neo-tree/neo-tree.nvim",
      opts = function(_, opts)
        local function on_move(data)
          Snacks.rename.on_rename_file(data.source, data.destination)
        end
        local events = require("neo-tree.events")
        opts.event_handlers = opts.event_handlers or {}
        vim.list_extend(opts.event_handlers, {
          { event = events.FILE_MOVED, handler = on_move },
          { event = events.FILE_RENAMED, handler = on_move },
        })
      end,
    }
<


==============================================================================
5. nvim-tree                                         *snacks-rename-nvim-tree*

>lua
    local prev = { new_name = "", old_name = "" } -- Prevents duplicate events
    vim.api.nvim_create_autocmd("User", {
      pattern = "NvimTreeSetup",
      callback = function()
        local events = require("nvim-tree.api").events
        events.subscribe(events.Event.NodeRenamed, function(data)
          if prev.new_name ~= data.new_name or prev.old_name ~= data.old_name then
            data = data
            Snacks.rename.on_rename_file(data.old_name, data.new_name)
          end
        end)
      end,
    })
<


==============================================================================
6. Module                                               *snacks-rename-module*


`Snacks.rename.on_rename_file()`                   *Snacks.rename.on_rename_file()*

Lets LSP clients know that a file has been renamed

>lua
    ---@param from string
    ---@param to string
    ---@param rename? fun()
    Snacks.rename.on_rename_file(from, to, rename)
<


`Snacks.rename.rename_file()`                    *Snacks.rename.rename_file()*

Renames the provided file, or the current buffer’s file. Prompt for the new
filename if `to` is not provided. do the rename, and trigger LSP handlers

>lua
    ---@param opts? {from?: string, to?:string, on_rename?: fun(to:string, from:string, ok:boolean)}
    Snacks.rename.rename_file(opts)
<

Generated by panvimdoc <https://github.com/kdheepak/panvimdoc>

vim:tw=78:ts=8:noet:ft=help:norl:
