*snacks-terminal*                                          snacks_terminal

==============================================================================
Table of Contents                          *snacks-terminal-table-of-contents*

1. Usage                                               |snacks-terminal-usage|
  - Edgy Integration                  |snacks-terminal-usage-edgy-integration|
2. Setup                                               |snacks-terminal-setup|
3. Config                                             |snacks-terminal-config|
4. Styles                                             |snacks-terminal-styles|
  - terminal                                 |snacks-terminal-styles-terminal|
5. Types                                               |snacks-terminal-types|
6. Module                                             |snacks-terminal-module|
  - Snacks.terminal()               |snacks-terminal-module-snacks.terminal()|
  - Snacks.terminal.colorize()|snacks-terminal-module-snacks.terminal.colorize()|
  - Snacks.terminal.get()       |snacks-terminal-module-snacks.terminal.get()|
  - Snacks.terminal.list()     |snacks-terminal-module-snacks.terminal.list()|
  - Snacks.terminal.open()     |snacks-terminal-module-snacks.terminal.open()|
  - Snacks.terminal.toggle() |snacks-terminal-module-snacks.terminal.toggle()|
7. Links                                               |snacks-terminal-links|
Create and toggle terminal windows.

Based on the provided options, some defaults will be set:

- if no `cmd` is provided, the window will be opened in a bottom split
- if `cmd` is provided, the window will be opened in a floating window
- for splits, a `winbar` will be added with the terminal title


==============================================================================
1. Usage                                               *snacks-terminal-usage*


EDGY INTEGRATION                      *snacks-terminal-usage-edgy-integration*

>lua
    {
      "folke/edgy.nvim",
      ---@module 'edgy'
      ---@param opts Edgy.Config
      opts = function(_, opts)
        for _, pos in ipairs({ "top", "bottom", "left", "right" }) do
          opts[pos] = opts[pos] or {}
          table.insert(opts[pos], {
            ft = "snacks_terminal",
            size = { height = 0.4 },
            title = "%{b:snacks_terminal.id}: %{b:term_title}",
            filter = function(_buf, win)
              return vim.w[win].snacks_win
                and vim.w[win].snacks_win.position == pos
                and vim.w[win].snacks_win.relative == "editor"
                and not vim.w[win].trouble_preview
            end,
          })
        end
      end,
    }
<


==============================================================================
2. Setup                                               *snacks-terminal-setup*

>lua
    -- lazy.nvim
    {
      "folke/snacks.nvim",
      ---@type snacks.Config
      opts = {
        terminal = {
          -- your terminal configuration comes here
          -- or leave it empty to use the default settings
          -- refer to the configuration section below
        }
      }
    }
<


==============================================================================
3. Config                                             *snacks-terminal-config*

>lua
    ---@class snacks.terminal.Config
    ---@field win? snacks.win.Config|{}
    ---@field shell? string|string[] The shell to use. Defaults to `vim.o.shell`
    ---@field override? fun(cmd?: string|string[], opts?: snacks.terminal.Opts) Use this to use a different terminal implementation
    {
      win = { style = "terminal" },
    }
<


==============================================================================
4. Styles                                             *snacks-terminal-styles*

Check the styles
<https://github.com/folke/snacks.nvim/blob/main/docs/styles.md> docs for more
information on how to customize these styles


TERMINAL                                     *snacks-terminal-styles-terminal*

>lua
    {
      bo = {
        filetype = "snacks_terminal",
      },
      wo = {},
      keys = {
        q = "hide",
        gf = function(self)
          local f = vim.fn.findfile(vim.fn.expand("<cfile>"), "**")
          if f == "" then
            Snacks.notify.warn("No file under cursor")
          else
            self:hide()
            vim.schedule(function()
              vim.cmd("e " .. f)
            end)
          end
        end,
        term_normal = {
          "<esc>",
          function(self)
            self.esc_timer = self.esc_timer or (vim.uv or vim.loop).new_timer()
            if self.esc_timer:is_active() then
              self.esc_timer:stop()
              vim.cmd("stopinsert")
            else
              self.esc_timer:start(200, 0, function() end)
              return "<esc>"
            end
          end,
          mode = "t",
          expr = true,
          desc = "Double escape to normal mode",
        },
      },
    }
<


==============================================================================
5. Types                                               *snacks-terminal-types*

>lua
    ---@class snacks.terminal.Opts: snacks.terminal.Config
    ---@field cwd? string
    ---@field env? table<string, string>
    ---@field start_insert? boolean start insert mode when starting the terminal
    ---@field auto_insert? boolean start insert mode when entering the terminal buffer
    ---@field auto_close? boolean close the terminal buffer when the process exits
    ---@field interactive? boolean shortcut for `start_insert`, `auto_close` and `auto_insert` (default: true)
<


==============================================================================
6. Module                                             *snacks-terminal-module*

>lua
    ---@class snacks.terminal: snacks.win
    ---@field cmd? string | string[]
    ---@field opts snacks.terminal.Opts
    Snacks.terminal = {}
<


`Snacks.terminal()`                                        *Snacks.terminal()*

>lua
    ---@type fun(cmd?: string|string[], opts?: snacks.terminal.Opts): snacks.terminal
    Snacks.terminal()
<


`Snacks.terminal.colorize()`                      *Snacks.terminal.colorize()*

Colorize the current buffer. Replaces ansii color codes with the actual colors.

Example:

>sh
    ls -la --color=always | nvim - -c "lua Snacks.terminal.colorize()"
<

>lua
    Snacks.terminal.colorize()
<


`Snacks.terminal.get()`                                *Snacks.terminal.get()*

Get or create a terminal window. The terminal id is based on the `cmd`, `cwd`,
`env` and `vim.v.count1` options. `opts.create` defaults to `true`.

>lua
    ---@param cmd? string | string[]
    ---@param opts? snacks.terminal.Opts| {create?: boolean}
    ---@return snacks.win? terminal, boolean? created
    Snacks.terminal.get(cmd, opts)
<


`Snacks.terminal.list()`                              *Snacks.terminal.list()*

>lua
    ---@return snacks.win[]
    Snacks.terminal.list()
<


`Snacks.terminal.open()`                              *Snacks.terminal.open()*

Open a new terminal window.

>lua
    ---@param cmd? string | string[]
    ---@param opts? snacks.terminal.Opts
    Snacks.terminal.open(cmd, opts)
<


`Snacks.terminal.toggle()`                          *Snacks.terminal.toggle()*

Toggle a terminal window. The terminal id is based on the `cmd`, `cwd`, `env`
and `vim.v.count1` options.

>lua
    ---@param cmd? string | string[]
    ---@param opts? snacks.terminal.Opts
    Snacks.terminal.toggle(cmd, opts)
<

==============================================================================
7. Links                                               *snacks-terminal-links*

1. *image*: https://github.com/user-attachments/assets/afcc9989-57d7-4518-a390-cc7d6f0cec13

Generated by panvimdoc <https://github.com/kdheepak/panvimdoc>

vim:tw=78:ts=8:noet:ft=help:norl:
