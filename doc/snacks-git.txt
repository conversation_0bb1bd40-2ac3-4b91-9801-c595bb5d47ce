*snacks-git*                                                    snacks_git

==============================================================================
Table of Contents                               *snacks-git-table-of-contents*

1. Styles                                                  |snacks-git-styles|
  - blame_line                                  |snacks-git-styles-blame_line|
2. Module                                                  |snacks-git-module|
  - Snacks.git.blame_line()        |snacks-git-module-snacks.git.blame_line()|
  - Snacks.git.get_root()            |snacks-git-module-snacks.git.get_root()|

==============================================================================
1. Styles                                                  *snacks-git-styles*

Check the styles
<https://github.com/folke/snacks.nvim/blob/main/docs/styles.md> docs for more
information on how to customize these styles


BLAME_LINE                                      *snacks-git-styles-blame_line*

>lua
    {
      width = 0.6,
      height = 0.6,
      border = "rounded",
      title = " Git Blame ",
      title_pos = "center",
      ft = "git",
    }
<


==============================================================================
2. Module                                                  *snacks-git-module*


`Snacks.git.blame_line()`                            *Snacks.git.blame_line()*

Show git log for the current line.

>lua
    ---@param opts? snacks.terminal.Opts | {count?: number}
    Snacks.git.blame_line(opts)
<


`Snacks.git.get_root()`                                *Snacks.git.get_root()*

Gets the git root for a buffer or path. Defaults to the current buffer.

>lua
    ---@param path? number|string buffer or path
    ---@return string?
    Snacks.git.get_root(path)
<

Generated by panvimdoc <https://github.com/kdheepak/panvimdoc>

vim:tw=78:ts=8:noet:ft=help:norl:
