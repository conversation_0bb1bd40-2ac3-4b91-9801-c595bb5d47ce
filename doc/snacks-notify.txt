*snacks-notify*                                              snacks_notify

==============================================================================
Table of Contents                            *snacks-notify-table-of-contents*

1. Types                                                 |snacks-notify-types|
2. Module                                               |snacks-notify-module|
  - Snacks.notify()                     |snacks-notify-module-snacks.notify()|
  - Snacks.notify.error()         |snacks-notify-module-snacks.notify.error()|
  - Snacks.notify.info()           |snacks-notify-module-snacks.notify.info()|
  - Snacks.notify.notify()       |snacks-notify-module-snacks.notify.notify()|
  - Snacks.notify.warn()           |snacks-notify-module-snacks.notify.warn()|

==============================================================================
1. Types                                                 *snacks-notify-types*

>lua
    ---@alias snacks.notify.Opts snacks.notifier.Notif.opts|{once?: boolean}
<


==============================================================================
2. Module                                               *snacks-notify-module*


`Snacks.notify()`                                            *Snacks.notify()*

>lua
    ---@type fun(msg: string|string[], opts?: snacks.notify.Opts)
    Snacks.notify()
<


`Snacks.notify.error()`                                *Snacks.notify.error()*

>lua
    ---@param msg string|string[]
    ---@param opts? snacks.notify.Opts
    Snacks.notify.error(msg, opts)
<


`Snacks.notify.info()`                                  *Snacks.notify.info()*

>lua
    ---@param msg string|string[]
    ---@param opts? snacks.notify.Opts
    Snacks.notify.info(msg, opts)
<


`Snacks.notify.notify()`                              *Snacks.notify.notify()*

>lua
    ---@param msg string|string[]
    ---@param opts? snacks.notify.Opts
    Snacks.notify.notify(msg, opts)
<


`Snacks.notify.warn()`                                  *Snacks.notify.warn()*

>lua
    ---@param msg string|string[]
    ---@param opts? snacks.notify.Opts
    Snacks.notify.warn(msg, opts)
<

Generated by panvimdoc <https://github.com/kdheepak/panvimdoc>

vim:tw=78:ts=8:noet:ft=help:norl:
