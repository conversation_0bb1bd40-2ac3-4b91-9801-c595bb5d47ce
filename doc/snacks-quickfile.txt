*snacks-quickfile*                                        snacks_quickfile

==============================================================================
Table of Contents                         *snacks-quickfile-table-of-contents*

1. Setup                                              |snacks-quickfile-setup|
2. Config                                            |snacks-quickfile-config|
When doing `nvim somefile.txt`, it will render the file as quickly as possible,
before loading your plugins.


==============================================================================
1. Setup                                              *snacks-quickfile-setup*

>lua
    -- lazy.nvim
    {
      "folke/snacks.nvim",
      ---@type snacks.Config
      opts = {
        quickfile = {
          -- your quickfile configuration comes here
          -- or leave it empty to use the default settings
          -- refer to the configuration section below
        }
      }
    }
<


==============================================================================
2. Config                                            *snacks-quickfile-config*

>lua
    ---@class snacks.quickfile.Config
    {
      -- any treesitter langs to exclude
      exclude = { "latex" },
    }
<

Generated by panvimdoc <https://github.com/kdheepak/panvimdoc>

vim:tw=78:ts=8:noet:ft=help:norl:
