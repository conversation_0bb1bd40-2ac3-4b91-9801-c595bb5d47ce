*snacks-dim*                                                    snacks_dim

==============================================================================
Table of Contents                               *snacks-dim-table-of-contents*

1. Setup                                                    |snacks-dim-setup|
2. Config                                                  |snacks-dim-config|
3. Module                                                  |snacks-dim-module|
  - Snacks.dim()                              |snacks-dim-module-snacks.dim()|
  - Snacks.dim.disable()              |snacks-dim-module-snacks.dim.disable()|
  - Snacks.dim.enable()                |snacks-dim-module-snacks.dim.enable()|
4. Links                                                    |snacks-dim-links|
Focus on the active scope by dimming the rest.

Similar plugins:

- twilight.nvim <https://github.com/folke/twilight.nvim>
- limelight.vim <https://github.com/junegunn/limelight.vim>
- goyo.vim <https://github.com/junegunn/goyo.vim>


==============================================================================
1. Setup                                                    *snacks-dim-setup*

>lua
    -- lazy.nvim
    {
      "folke/snacks.nvim",
      ---@type snacks.Config
      opts = {
        dim = {
          -- your dim configuration comes here
          -- or leave it empty to use the default settings
          -- refer to the configuration section below
        }
      }
    }
<


==============================================================================
2. Config                                                  *snacks-dim-config*

>lua
    ---@class snacks.dim.Config
    {
      ---@type snacks.scope.Config
      scope = {
        min_size = 5,
        max_size = 20,
        siblings = true,
      },
      -- animate scopes. Enabled by default for Neovim >= 0.10
      -- Works on older versions but has to trigger redraws during animation.
      ---@type snacks.animate.Config|{enabled?: boolean}
      animate = {
        enabled = vim.fn.has("nvim-0.10") == 1,
        easing = "outQuad",
        duration = {
          step = 20, -- ms per step
          total = 300, -- maximum duration
        },
      },
      -- what buffers to dim
      filter = function(buf)
        return vim.g.snacks_dim ~= false and vim.b[buf].snacks_dim ~= false and vim.bo[buf].buftype == ""
      end,
    }
<


==============================================================================
3. Module                                                  *snacks-dim-module*


`Snacks.dim()`                                                  *Snacks.dim()*

>lua
    ---@type fun(opts: snacks.dim.Config)
    Snacks.dim()
<


`Snacks.dim.disable()`                                  *Snacks.dim.disable()*

Disable dimming

>lua
    Snacks.dim.disable()
<


`Snacks.dim.enable()`                                    *Snacks.dim.enable()*

>lua
    ---@param opts? snacks.dim.Config
    Snacks.dim.enable(opts)
<

==============================================================================
4. Links                                                    *snacks-dim-links*

1. *image*: https://github.com/user-attachments/assets/c0c5ffda-aaeb-4578-8a18-abee2e443a93

Generated by panvimdoc <https://github.com/kdheepak/panvimdoc>

vim:tw=78:ts=8:noet:ft=help:norl:
