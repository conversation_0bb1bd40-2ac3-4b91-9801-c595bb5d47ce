*snacks-statuscolumn*                                  snacks_statuscolumn

==============================================================================
Table of Contents                      *snacks-statuscolumn-table-of-contents*

1. Setup                                           |snacks-statuscolumn-setup|
2. Config                                         |snacks-statuscolumn-config|
3. Types                                           |snacks-statuscolumn-types|
4. Module                                         |snacks-statuscolumn-module|
  - Snacks.statuscolumn()   |snacks-statuscolumn-module-snacks.statuscolumn()|
  - Snacks.statuscolumn.click_fold()|snacks-statuscolumn-module-snacks.statuscolumn.click_fold()|
  - Snacks.statuscolumn.get()|snacks-statuscolumn-module-snacks.statuscolumn.get()|

==============================================================================
1. Setup                                           *snacks-statuscolumn-setup*

>lua
    -- lazy.nvim
    {
      "folke/snacks.nvim",
      ---@type snacks.Config
      opts = {
        statuscolumn = {
          -- your statuscolumn configuration comes here
          -- or leave it empty to use the default settings
          -- refer to the configuration section below
        }
      }
    }
<


==============================================================================
2. Config                                         *snacks-statuscolumn-config*

>lua
    ---@class snacks.statuscolumn.Config
    ---@field left snacks.statuscolumn.Components
    ---@field right snacks.statuscolumn.Components
    ---@field enabled? boolean
    {
      left = { "mark", "sign" }, -- priority of signs on the left (high to low)
      right = { "fold", "git" }, -- priority of signs on the right (high to low)
      folds = {
        open = false, -- show open fold icons
        git_hl = false, -- use Git Signs hl for fold icons
      },
      git = {
        -- patterns to match Git signs
        patterns = { "GitSign", "MiniDiffSign" },
      },
      refresh = 50, -- refresh at most every 50ms
    }
<


==============================================================================
3. Types                                           *snacks-statuscolumn-types*

>lua
    ---@alias snacks.statuscolumn.Component "mark"|"sign"|"fold"|"git"
    ---@alias snacks.statuscolumn.Components snacks.statuscolumn.Component[]|fun(win:number,buf:number,lnum:number):snacks.statuscolumn.Component[]
<


==============================================================================
4. Module                                         *snacks-statuscolumn-module*


`Snacks.statuscolumn()`                                *Snacks.statuscolumn()*

>lua
    ---@type fun(): string
    Snacks.statuscolumn()
<


`Snacks.statuscolumn.click_fold()`                         *Snacks.statuscolumn.click_fold()*

>lua
    Snacks.statuscolumn.click_fold()
<


`Snacks.statuscolumn.get()`                         *Snacks.statuscolumn.get()*

>lua
    Snacks.statuscolumn.get()
<

Generated by panvimdoc <https://github.com/kdheepak/panvimdoc>

vim:tw=78:ts=8:noet:ft=help:norl:
