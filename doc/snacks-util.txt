*snacks-util*                                                  snacks_util

==============================================================================
Table of Contents                              *snacks-util-table-of-contents*

1. Types                                                   |snacks-util-types|
2. Module                                                 |snacks-util-module|
  - Snacks.util.blend()               |snacks-util-module-snacks.util.blend()|
  - Snacks.util.bo()                     |snacks-util-module-snacks.util.bo()|
  - Snacks.util.color()               |snacks-util-module-snacks.util.color()|
  - Snacks.util.debounce()         |snacks-util-module-snacks.util.debounce()|
  - Snacks.util.file_decode()   |snacks-util-module-snacks.util.file_decode()|
  - Snacks.util.file_encode()   |snacks-util-module-snacks.util.file_encode()|
  - Snacks.util.icon()                 |snacks-util-module-snacks.util.icon()|
  - Snacks.util.is_float()         |snacks-util-module-snacks.util.is_float()|
  - Snacks.util.is_transparent()|snacks-util-module-snacks.util.is_transparent()|
  - Snacks.util.keycode()           |snacks-util-module-snacks.util.keycode()|
  - Snacks.util.normkey()           |snacks-util-module-snacks.util.normkey()|
  - Snacks.util.on_key()             |snacks-util-module-snacks.util.on_key()|
  - Snacks.util.on_module()       |snacks-util-module-snacks.util.on_module()|
  - Snacks.util.redraw()             |snacks-util-module-snacks.util.redraw()|
  - Snacks.util.redraw_range() |snacks-util-module-snacks.util.redraw_range()|
  - Snacks.util.ref()                   |snacks-util-module-snacks.util.ref()|
  - Snacks.util.set_hl()             |snacks-util-module-snacks.util.set_hl()|
  - Snacks.util.spinner()           |snacks-util-module-snacks.util.spinner()|
  - Snacks.util.throttle()         |snacks-util-module-snacks.util.throttle()|
  - Snacks.util.var()                   |snacks-util-module-snacks.util.var()|
  - Snacks.util.winhl()               |snacks-util-module-snacks.util.winhl()|
  - Snacks.util.wo()                     |snacks-util-module-snacks.util.wo()|

==============================================================================
1. Types                                                   *snacks-util-types*

>lua
    ---@alias snacks.util.hl table<string, string|vim.api.keyset.highlight>
<


==============================================================================
2. Module                                                 *snacks-util-module*


`Snacks.util.blend()`                                    *Snacks.util.blend()*

>lua
    ---@param fg string foreground color
    ---@param bg string background color
    ---@param alpha number number between 0 and 1. 0 results in bg, 1 results in fg
    Snacks.util.blend(fg, bg, alpha)
<


`Snacks.util.bo()`                                          *Snacks.util.bo()*

Set buffer-local options.

>lua
    ---@param buf number
    ---@param bo vim.bo|{}
    Snacks.util.bo(buf, bo)
<


`Snacks.util.color()`                                    *Snacks.util.color()*

>lua
    ---@param group string|string[] hl group to get color from
    ---@param prop? string property to get. Defaults to "fg"
    Snacks.util.color(group, prop)
<


`Snacks.util.debounce()`                              *Snacks.util.debounce()*

>lua
    ---@generic T
    ---@param fn T
    ---@param opts? {ms?:number}
    ---@return T
    Snacks.util.debounce(fn, opts)
<


`Snacks.util.file_decode()`                        *Snacks.util.file_decode()*

Decodes a file name to a string.

>lua
    ---@param str string
    Snacks.util.file_decode(str)
<


`Snacks.util.file_encode()`                        *Snacks.util.file_encode()*

Encodes a string to be used as a file name.

>lua
    ---@param str string
    Snacks.util.file_encode(str)
<


`Snacks.util.icon()`                                      *Snacks.util.icon()*

Get an icon from `mini.icons` or `nvim-web-devicons`.

>lua
    ---@param name string
    ---@param cat? string defaults to "file"
    ---@param opts? { fallback?: {dir?:string, file?:string} }
    ---@return string, string?
    Snacks.util.icon(name, cat, opts)
<


`Snacks.util.is_float()`                              *Snacks.util.is_float()*

>lua
    ---@param win? number
    Snacks.util.is_float(win)
<


`Snacks.util.is_transparent()`                  *Snacks.util.is_transparent()*

Check if the colorscheme is transparent.

>lua
    Snacks.util.is_transparent()
<


`Snacks.util.keycode()`                                *Snacks.util.keycode()*

>lua
    ---@param str string
    Snacks.util.keycode(str)
<


`Snacks.util.normkey()`                                *Snacks.util.normkey()*

>lua
    ---@param key string
    Snacks.util.normkey(key)
<


`Snacks.util.on_key()`                                  *Snacks.util.on_key()*

>lua
    ---@param key string
    ---@param cb fun(key:string)
    Snacks.util.on_key(key, cb)
<


`Snacks.util.on_module()`                            *Snacks.util.on_module()*

Call a function when a module is loaded. The callback is called immediately if
the module is already loaded. Otherwise, it is called when the module is
loaded.

>lua
    ---@param modname string
    ---@param cb fun(modname:string)
    Snacks.util.on_module(modname, cb)
<


`Snacks.util.redraw()`                                  *Snacks.util.redraw()*

Redraw the window. Optimized for Neovim >= 0.10

>lua
    ---@param win number
    Snacks.util.redraw(win)
<


`Snacks.util.redraw_range()`                      *Snacks.util.redraw_range()*

Redraw the range of lines in the window. Optimized for Neovim >= 0.10

>lua
    ---@param win number
    ---@param from number -- 1-indexed, inclusive
    ---@param to number -- 1-indexed, inclusive
    Snacks.util.redraw_range(win, from, to)
<


`Snacks.util.ref()`                                        *Snacks.util.ref()*

>lua
    ---@generic T
    ---@param t T
    ---@return { value?:T }|fun():T?
    Snacks.util.ref(t)
<


`Snacks.util.set_hl()`                                  *Snacks.util.set_hl()*

Ensures the hl groups are always set, even after a colorscheme change.

>lua
    ---@param groups snacks.util.hl
    ---@param opts? { prefix?:string, default?:boolean, managed?:boolean }
    Snacks.util.set_hl(groups, opts)
<


`Snacks.util.spinner()`                                *Snacks.util.spinner()*

>lua
    Snacks.util.spinner()
<


`Snacks.util.throttle()`                              *Snacks.util.throttle()*

>lua
    ---@generic T
    ---@param fn T
    ---@param opts? {ms?:number}
    ---@return T
    Snacks.util.throttle(fn, opts)
<


`Snacks.util.var()`                                        *Snacks.util.var()*

Get a buffer or global variable.

>lua
    ---@generic T
    ---@param buf? number
    ---@param name string
    ---@param default? T
    ---@return T
    Snacks.util.var(buf, name, default)
<


`Snacks.util.winhl()`                                    *Snacks.util.winhl()*

Merges vim.wo.winhighlight options. Option values can be a string or a
dictionary.

>lua
    ---@param ... string|table<string, string>
    Snacks.util.winhl(...)
<


`Snacks.util.wo()`                                          *Snacks.util.wo()*

Set window-local options.

>lua
    ---@param win number
    ---@param wo vim.wo|{}|{winhighlight: string|table<string, string>}
    Snacks.util.wo(win, wo)
<

Generated by panvimdoc <https://github.com/kdheepak/panvimdoc>

vim:tw=78:ts=8:noet:ft=help:norl:
