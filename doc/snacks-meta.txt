*snacks-meta*                                                  snacks_meta

==============================================================================
Table of Contents                              *snacks-meta-table-of-contents*

1. Types                                                   |snacks-meta-types|
2. Module                                                 |snacks-meta-module|
  - Snacks.meta.file()                 |snacks-meta-module-snacks.meta.file()|
  - Snacks.meta.get()                   |snacks-meta-module-snacks.meta.get()|
Meta functions for Snacks


==============================================================================
1. Types                                                   *snacks-meta-types*

>lua
    ---@class snacks.meta.Meta
    ---@field desc string
    ---@field needs_setup? boolean
    ---@field hide? boolean
    ---@field readme? boolean
    ---@field docs? boolean
    ---@field health? boolean
    ---@field types? boolean
    ---@field config? boolean
    ---@field merge? { [string|number]: string }
<

>lua
    ---@class snacks.meta.Plugin
    ---@field name string
    ---@field file string
    ---@field meta snacks.meta.Meta
    ---@field health? fun()
<


==============================================================================
2. Module                                                 *snacks-meta-module*


`Snacks.meta.file()`                                      *Snacks.meta.file()*

>lua
    Snacks.meta.file(name)
<


`Snacks.meta.get()`                                        *Snacks.meta.get()*

Get the metadata for all snacks plugins

>lua
    ---@return snacks.meta.Plugin[]
    Snacks.meta.get()
<

Generated by panvimdoc <https://github.com/kdheepak/panvimdoc>

vim:tw=78:ts=8:noet:ft=help:norl:
