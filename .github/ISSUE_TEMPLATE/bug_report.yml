name: Bug Report
description: File a bug/issue
title: "bug: "
labels: [bug]
body:
  - type: markdown
    attributes:
      value: |
        **Before** reporting an issue, make sure to read the [documentation](https://github.com/folke/snacks.nvim)
        and search [existing issues](https://github.com/folke/snacks.nvim/issues).

        Usage questions such as ***"How do I...?"*** belong in [Discussions](https://github.com/folke/snacks.nvim/discussions) and will be closed.
  - type: checkboxes
    attributes:
      label: Did you check docs and existing issues?
      description: Make sure you checked all of the below before submitting an issue
      options:
        - label: I have read all the snacks.nvim docs
          required: true
        - label: I have updated the plugin to the latest version before submitting this issue
          required: true
        - label: I have searched the existing issues of snacks.nvim
          required: true
        - label: I have searched the existing issues of plugins related to this issue
          required: true
  - type: input
    attributes:
      label: "Neovim version (nvim -v)"
      placeholder: "0.8.0 commit db1b0ee3b30f"
    validations:
      required: true
  - type: input
    attributes:
      label: "Operating system/version"
      placeholder: "MacOS 11.5"
    validations:
      required: true
  - type: textarea
    attributes:
      label: Describe the bug
      description: A clear and concise description of what the bug is. Please include any related errors you see in Neovim.
    validations:
      required: true
  - type: textarea
    attributes:
      label: Steps To Reproduce
      description: Steps to reproduce the behavior.
      placeholder: |
        1.
        2. 
        3.
    validations:
      required: true
  - type: textarea
    attributes:
      label: Expected Behavior
      description: A concise description of what you expected to happen.
    validations:
      required: true
  - type: textarea
    attributes:
      label: Repro
      description: Minimal `init.lua` to reproduce this issue. Save as `repro.lua` and run with `nvim -u repro.lua`
      value: |
        vim.env.LAZY_STDPATH = ".repro"
        load(vim.fn.system("curl -s https://raw.githubusercontent.com/folke/lazy.nvim/main/bootstrap.lua"))()

        require("lazy.minit").repro({
          spec = {
            { "folke/snacks.nvim", opts = {} },
            -- add any other plugins here
          },
        })
      render: lua
    validations:
      required: false
