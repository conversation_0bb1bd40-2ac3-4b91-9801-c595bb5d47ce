# Changelog

## [2.22.0](https://github.com/folke/snacks.nvim/compare/v2.21.0...v2.22.0) (2025-02-25)


### Features

* **image:** allow disabling math rendering. Closes [#1247](https://github.com/folke/snacks.nvim/issues/1247) ([1543a06](https://github.com/folke/snacks.nvim/commit/1543a063fbd3a462879d696b2885f4aa90c55896))
* **image:** configurable templates for math expressions. Closes [#1338](https://github.com/folke/snacks.nvim/issues/1338) ([e039139](https://github.com/folke/snacks.nvim/commit/e039139291f85eebf3eeb41cc5ad9dc4265cafa4))
* **image:** removed `org` integration, since that is now handled by the org mode plugin directly. ([956fe69](https://github.com/folke/snacks.nvim/commit/956fe69df328d2da924a04061802fb7d2ec5fef6))
* **picker.input:** added some ctrl+r keymaps similar to cmdline. Closes [#1420](https://github.com/folke/snacks.nvim/issues/1420) ([c864a7d](https://github.com/folke/snacks.nvim/commit/c864a7d378da2a11afb09302b3220264e2aa3409))
* **util:** util method to check if ts lang is available on any Neovim version. See [#1422](https://github.com/folke/snacks.nvim/issues/1422) ([e2cb9df](https://github.com/folke/snacks.nvim/commit/e2cb9df7d0695911f60a4510191aaf4a3d0d81ad))


### Bug Fixes

* **compat:** fixup ([ceabfc1](https://github.com/folke/snacks.nvim/commit/ceabfc1b89fe8e46b5138ae2417890121f5dfa02))
* **compat:** properly detect async treesitter parsing ([842605f](https://github.com/folke/snacks.nvim/commit/842605f072e5d124a47eeb212bc2f78345bec4c4))
* **compat:** vim.fs.normalize. Closes [#1321](https://github.com/folke/snacks.nvim/issues/1321) ([2295cfc](https://github.com/folke/snacks.nvim/commit/2295cfcca5bc749f169fb83ca4bdea9a85ad79a3))
* **dim:** check that win is valid when animating dim. Closes [#1342](https://github.com/folke/snacks.nvim/issues/1342) ([47e1440](https://github.com/folke/snacks.nvim/commit/47e1440d547233772a3958580d429b38b5959edd))
* **image.placement:** max width/height in cells is 297. Closes [#1345](https://github.com/folke/snacks.nvim/issues/1345) ([5fa93cb](https://github.com/folke/snacks.nvim/commit/5fa93cb6846b5998bc0b4b4ac9de47108fe39ce6))
* **image.terminal:** reset queue when timer runs ([2b34c4d](https://github.com/folke/snacks.nvim/commit/2b34c4dc05aa4cbccc6171fa530e95c218e9bc9c))
* **image.terminal:** write queued terminal output on main ([1b63b18](https://github.com/folke/snacks.nvim/commit/1b63b1811c58f661ad22f390a52aa6723703dc3d))
* **picker.buffers:** add `a` flag when buffer is visible in a window. See [#1417](https://github.com/folke/snacks.nvim/issues/1417) ([91c3da0](https://github.com/folke/snacks.nvim/commit/91c3da0b4b286967d6d0166c0fc5769795a78918))
* **picker.recent:** expand to full path before normalizing. Closes [#1406](https://github.com/folke/snacks.nvim/issues/1406) ([cf47fa7](https://github.com/folke/snacks.nvim/commit/cf47fa7cee80b0952706aacd4068310fe041761e))
* **picker:** allow overriding winhl of layout box wins. Closes [#1424](https://github.com/folke/snacks.nvim/issues/1424) ([b0f983e](https://github.com/folke/snacks.nvim/commit/b0f983ef9aa9b9855ff0b72350cd3dc80de70675))
* **picker:** disable regex for grep_word ([#1363](https://github.com/folke/snacks.nvim/issues/1363)) ([54298eb](https://github.com/folke/snacks.nvim/commit/54298eb624bd89f10f288b92560861277a34116d))
* **picker:** remove unused keymaps for mouse scrolling ([33df54d](https://github.com/folke/snacks.nvim/commit/33df54dae71df7f7ec17551c23ad0ffc677e6ad1))
* **picker:** update titles before showing. Closes [#1337](https://github.com/folke/snacks.nvim/issues/1337) ([3ae9863](https://github.com/folke/snacks.nvim/commit/3ae98636aaaf8f1b2f55b264f5745ae268de532f))
* **scope:** use `rawequal` to check if scope impl is treesitter. Closes [#1413](https://github.com/folke/snacks.nvim/issues/1413) ([4ce197b](https://github.com/folke/snacks.nvim/commit/4ce197bff9cb9b78a0bdcebb6f7ebbf22cd48c6a))
* **scroll:** compat with Neovim 0.9.4 ([4c52b7f](https://github.com/folke/snacks.nvim/commit/4c52b7f25da0ce6b2b830ce060dbd162706acf33))
* **statuscolumn:** right-align the current line number when relativenumber=true. Closes [#1376](https://github.com/folke/snacks.nvim/issues/1376) ([dd15e3a](https://github.com/folke/snacks.nvim/commit/dd15e3a05a2111231c53726f18e39a147162c20f))
* **win:** don't update title is relative win is invalid. Closes [#1348](https://github.com/folke/snacks.nvim/issues/1348) ([a00c323](https://github.com/folke/snacks.nvim/commit/a00c323d4b244f781df6df8b11bbfa47f63202d4))
* **win:** use correct keys for displaying help. Closes [#1364](https://github.com/folke/snacks.nvim/issues/1364) ([b100c93](https://github.com/folke/snacks.nvim/commit/b100c937177536cf2aa634ddd2aa5b8a1dd23ace))
* **zen:** always count cmdheight towards Zen bottom offset ([#1402](https://github.com/folke/snacks.nvim/issues/1402)) ([041bf1d](https://github.com/folke/snacks.nvim/commit/041bf1da9ed12498cbe3273dd90ef83e0a4913fa))


### Performance Improvements

* **scope:** use async treesitter parsing when available ([e0f882e](https://github.com/folke/snacks.nvim/commit/e0f882e6d6464666319502151cc244a090d4377f))

## 2.21.0 (2025-02-20)


### Features

* added new `image` snacks plugin for the kitty graphics protocol ([4e4e630](https://github.com/folke/snacks.nvim/commit/4e4e63048e5ddae6f921f1a1b4bd11a53016c7aa))
* **bigfile:** configurable average line length (default = 1000). Useful for minified files. Closes [#576](https://github.com/folke/snacks.nvim/issues/576). Closes [#372](https://github.com/folke/snacks.nvim/issues/372) ([7fa92a2](https://github.com/folke/snacks.nvim/commit/7fa92a24501fa85b567c130b4e026f9ca1efed17))
* **compat:** added `svim`, a compatibility layer for Neovim. Closes [#1321](https://github.com/folke/snacks.nvim/issues/1321) ([bc902f7](https://github.com/folke/snacks.nvim/commit/bc902f7032df305df7dc48104cfa4e37967b3bdf))
* **debug:** graduate proc debug to Snacks.debug.cmd ([eced303](https://github.com/folke/snacks.nvim/commit/eced3033ea29bf9154a5f2c5207bf9fc97368599))
* **explorer:** `opts.include` and `opts.exclude`. Closes [#1068](https://github.com/folke/snacks.nvim/issues/1068) ([ab1889c](https://github.com/folke/snacks.nvim/commit/ab1889c35b1845f487f31f0399ec0c8bd2c6e521))
* **explorer:** added `Snacks.explorer.reveal()` to reveal the current file in the tree. ([b4cf6bb](https://github.com/folke/snacks.nvim/commit/b4cf6bb48d882a873a6954bff2802d88e8e19e0d))
* **explorer:** added copy/paste (yank/paste) for files. Closes [#1195](https://github.com/folke/snacks.nvim/issues/1195) ([938aee4](https://github.com/folke/snacks.nvim/commit/938aee4a02119ad693a67c38b64a9b3232a72565))
* **explorer:** added ctrl+f to grep in the item's directory ([0454b21](https://github.com/folke/snacks.nvim/commit/0454b21165cb84d2f59a1daf6226de065c90d4f7))
* **explorer:** added ctrl+t to open a terminal in the item's directory ([81f9006](https://github.com/folke/snacks.nvim/commit/81f90062c50430c1bad9546fcb65c3e43a76be9b))
* **explorer:** added diagnostics file/directory status ([7f1b60d](https://github.com/folke/snacks.nvim/commit/7f1b60d5576345af5e7b990f3a9e4bca49cd3686))
* **explorer:** added quick nav with `[`, `]` with `d/w/e` for diagnostics ([d1d5585](https://github.com/folke/snacks.nvim/commit/d1d55850ecb4aac1396c314a159db1e90a34bd79))
* **explorer:** added support for live search ([82c4a50](https://github.com/folke/snacks.nvim/commit/82c4a50985c9bb9f4b1d598f10a30e1122a35212))
* **explorer:** allow disabling untracked git status. Closes [#983](https://github.com/folke/snacks.nvim/issues/983) ([a3b083b](https://github.com/folke/snacks.nvim/commit/a3b083b8443b1ae1299747fdac8da51c3160835b))
* **explorer:** deal with existing buffers when renaming / deleting files. Closes [#1315](https://github.com/folke/snacks.nvim/issues/1315) ([6614a2c](https://github.com/folke/snacks.nvim/commit/6614a2c84f1ad8528aa03caeb2574b274ee0c20b))
* **explorer:** different hl group for broken links ([1989921](https://github.com/folke/snacks.nvim/commit/1989921466e6b5234ae8f71add41b8defd55f732))
* **explorer:** disable fuzzy searches by default for explorer since it's too noisy and we can't sort on score due to tree view ([b07788f](https://github.com/folke/snacks.nvim/commit/b07788f14a28daa8d0b387c1258f8f348f47420f))
* **explorer:** file watching that works on all platforms ([8399465](https://github.com/folke/snacks.nvim/commit/8399465872c51fab54ad5d02eb315e258ec96ed1))
* **explorer:** focus on first file when searching in the explorer ([1d4bea4](https://github.com/folke/snacks.nvim/commit/1d4bea4a9ee8a5258c6ae085ac66dd5cc05a9749))
* **explorer:** git index watcher ([4c12475](https://github.com/folke/snacks.nvim/commit/4c12475e80528d8d48b9584d78d645e4a51c3298))
* **explorer:** show symlink target ([dfa79e0](https://github.com/folke/snacks.nvim/commit/dfa79e04436ebfdc83ba71c0048fc1636b4de5aa))
* **git_log:** add author filter ([#1091](https://github.com/folke/snacks.nvim/issues/1091)) ([8c11661](https://github.com/folke/snacks.nvim/commit/8c1166165b17376ed87f0dedfc480c7cb8e42b7c))
* **gitbrowse:** add support for git.sr.ht ([#1297](https://github.com/folke/snacks.nvim/issues/1297)) ([a3b47e5](https://github.com/folke/snacks.nvim/commit/a3b47e5202d924e6a6d4386bb5f94cc5857c4f8c))
* **gitbrowse:** open permalinks to files. Fixes [#320](https://github.com/folke/snacks.nvim/issues/320) ([#438](https://github.com/folke/snacks.nvim/issues/438)) ([2a06e4c](https://github.com/folke/snacks.nvim/commit/2a06e4ce9957dea555d38b4f52024ea9e2902d8e))
* **image.doc:** allow configuring the header for latex / typst inline in the document. Closes [#1303](https://github.com/folke/snacks.nvim/issues/1303) ([bde3add](https://github.com/folke/snacks.nvim/commit/bde3adddc7d787c5e93eb13af55b6e702d86418b))
* **image.doc:** allow setting `image.src` with `#set!`. Closes [#1276](https://github.com/folke/snacks.nvim/issues/1276) ([65f89e2](https://github.com/folke/snacks.nvim/commit/65f89e2d6f3790b0687f09ebe2811d953bd09e0c))
* **image.doc:** check for `image.ignore` in ts meta. See [#1276](https://github.com/folke/snacks.nvim/issues/1276) ([29c777a](https://github.com/folke/snacks.nvim/commit/29c777a0a0291a0caba17f2e9aeb86b6097fc83c))
* **image:** `conceal` option for inline rendering (disabled by default) ([684666f](https://github.com/folke/snacks.nvim/commit/684666f6432eae139b8ca6813b1a88679f8febc1))
* **image:** `Snacks.image.hover()` ([5f466be](https://github.com/folke/snacks.nvim/commit/5f466becd96ebcd0a52352f2d53206e0e86de35a))
* **image:** add support for `svelte` ([#1277](https://github.com/folke/snacks.nvim/issues/1277)) ([54ab77c](https://github.com/folke/snacks.nvim/commit/54ab77c5d2b2edefa29fc63de73c7b2b60d2651b))
* **image:** adde support for `Image` in jsx ([95878ad](https://github.com/folke/snacks.nvim/commit/95878ad32aaf310f465a004ef12e9edddf939287))
* **image:** added `opts.img_dirs` to configure the search path for resolving images. Closes [#1222](https://github.com/folke/snacks.nvim/issues/1222) ([ad0b88d](https://github.com/folke/snacks.nvim/commit/ad0b88dc0814dc760c7b6ed4efc7c2fa8d27ba76))
* **image:** added `Snacks.image.doc.at_cursor()`. See [#1108](https://github.com/folke/snacks.nvim/issues/1108) ([6348ccf](https://github.com/folke/snacks.nvim/commit/6348ccf1209739552f70800910c206637f3b2d2c))
* **image:** added fallback image rendering for wezterm. Closes [#1063](https://github.com/folke/snacks.nvim/issues/1063) ([9e6b1a6](https://github.com/folke/snacks.nvim/commit/9e6b1a62a87aa201dea13a755f6ac1ed680a20d1))
* **image:** added math rendering for typst. Closes [#1260](https://github.com/folke/snacks.nvim/issues/1260) ([e225823](https://github.com/folke/snacks.nvim/commit/e2258236a2fc770a87a81125232ca786ec7a1cf1))
* **image:** added proper support for tmux ([b1a3b66](https://github.com/folke/snacks.nvim/commit/b1a3b66fade926e9d211453275ddf1be19a847a5))
* **image:** added support for `.image` tags in neorg ([59bbe8d](https://github.com/folke/snacks.nvim/commit/59bbe8d90e91d4b4f63cc5fcb36c81bd8eeee850))
* **image:** added support for `typst`. Closes [#1235](https://github.com/folke/snacks.nvim/issues/1235) ([507c183](https://github.com/folke/snacks.nvim/commit/507c1836e3c5cfc5194bb6350ece1a1e0a1edf14))
* **image:** added support for a bunch of aditional languages ([a596f8a](https://github.com/folke/snacks.nvim/commit/a596f8a9ea0a058490bca8aca70f935cded18d22))
* **image:** added support for angle bracket urls. Closes [#1209](https://github.com/folke/snacks.nvim/issues/1209) ([14a1f32](https://github.com/folke/snacks.nvim/commit/14a1f32eafd50b5b6ae742052e6da04b1e0167b2))
* **image:** added support for math expressions in latex and markdown doc + images in latex. Closes [#1223](https://github.com/folke/snacks.nvim/issues/1223) ([1bca71a](https://github.com/folke/snacks.nvim/commit/1bca71a1332e3119ece0e62d668b75e6c98d948c))
* **image:** added support for mermaid diagrams in markdown ([f8e7942](https://github.com/folke/snacks.nvim/commit/f8e7942d6c83a1b1953320054102eb32bf536d98))
* **image:** added support for remote image viewing. Closes [#1156](https://github.com/folke/snacks.nvim/issues/1156) ([#1165](https://github.com/folke/snacks.nvim/issues/1165)) ([a5748ea](https://github.com/folke/snacks.nvim/commit/a5748ea8db2ac14fbc9c05376cc6d154d749f881))
* **image:** added support for tsx, jsx, vue and angular ([ab0ba5c](https://github.com/folke/snacks.nvim/commit/ab0ba5cb22d7bf62fa204f08426e601a20750f29))
* **image:** added support for wikilink style images. Closes [#1210](https://github.com/folke/snacks.nvim/issues/1210) ([3fda272](https://github.com/folke/snacks.nvim/commit/3fda27200d9af7ed181e9ee0a841c50137e9a5be))
* **image:** allow customizing font size for math expressions ([b052eb9](https://github.com/folke/snacks.nvim/commit/b052eb93728df6cc0c09b7ee42fec6d93477fc3e))
* **image:** allow customizing the default magick args for vector images ([2096fcd](https://github.com/folke/snacks.nvim/commit/2096fcdd739500ba8275d791b20d60f306c61b33))
* **image:** allow forcing image rendering even when the terminal support detection fails ([d17a6e4](https://github.com/folke/snacks.nvim/commit/d17a6e4af888c43ba3faddc30231aa2aebc699d4))
* **image:** apply image window options ([73366fa](https://github.com/folke/snacks.nvim/commit/73366fa17018d7fd4d115cec2466b2d8e7233341))
* **image:** better detection of image capabilities of the terminal/mux environment ([1795d4b](https://github.com/folke/snacks.nvim/commit/1795d4b1ec767886300faa4965539fe67318a06a))
* **image:** better error handling + option to disable error notifications ([1adfd29](https://github.com/folke/snacks.nvim/commit/1adfd29af3d1b4db2ba46f7a292410a2f9105fd6))
* **image:** better health checks ([d389c5d](https://github.com/folke/snacks.nvim/commit/d389c5df14d83b6aff9eb6734906888780e8ca71))
* **image:** check for `magick` in health check ([1284835](https://github.com/folke/snacks.nvim/commit/12848356c4fd672476f47d9dea9999784c140c05))
* **image:** custom `src` resolve function ([af21ea3](https://github.com/folke/snacks.nvim/commit/af21ea3ccf6c11246cfbb1bef061caa4f387f1f0))
* **image:** enabled pdf previews ([39bf513](https://github.com/folke/snacks.nvim/commit/39bf5131c4f8cd79c1779a5cb80e526cf9e4fffe))
* **image:** floats in markdown. Closes [#1151](https://github.com/folke/snacks.nvim/issues/1151) ([4e10e31](https://github.com/folke/snacks.nvim/commit/4e10e31398e6921ac19371099f06640b8753bc8a))
* **image:** health checks ([0d5b106](https://github.com/folke/snacks.nvim/commit/0d5b106d4eae756cd612fdabde36aa795a444546))
* **image:** images are now properly scaled based on device DPI and image DPI. Closing [#1257](https://github.com/folke/snacks.nvim/issues/1257) ([004050c](https://github.com/folke/snacks.nvim/commit/004050c43533ac38a224649268e913c6fb0c4caa))
* **image:** make manual hover work correctly ([942cb92](https://github.com/folke/snacks.nvim/commit/942cb9291e096d8604d515499e295ec67578b71a))
* **image:** make math packages configurable. Closes [#1295](https://github.com/folke/snacks.nvim/issues/1295) ([e27ba72](https://github.com/folke/snacks.nvim/commit/e27ba726b15e71eca700141c2030ac858bc8025c))
* **image:** markdown inline image preview. `opts.image` must be enabled and terminal needs support ([001f300](https://github.com/folke/snacks.nvim/commit/001f3002cabb9e23d8f1b23e0567db2d41c098a6))
* **image:** refactor + css/html + beter image fitting ([e35d6cd](https://github.com/folke/snacks.nvim/commit/e35d6cd4ba87e8ff71d6ebe52b7be53408e13538))
* **image:** refactor of treesitter queries to support inline image data ([0bf0c62](https://github.com/folke/snacks.nvim/commit/0bf0c6223d71ced4e5dc7ab7357b0a36a91a0a67))
* **images:** added support for org-mode. Closes [#1276](https://github.com/folke/snacks.nvim/issues/1276) ([10387af](https://github.com/folke/snacks.nvim/commit/10387af009e51678788506527b46240b2139fd7f))
* **image:** show progress indicator when converting image files ([b65178b](https://github.com/folke/snacks.nvim/commit/b65178b470385f0a81256d54c9d80f153cd14efd))
* **image:** try resolving paths relative to the document and to the cwd. See [#1203](https://github.com/folke/snacks.nvim/issues/1203) ([668cbbb](https://github.com/folke/snacks.nvim/commit/668cbbba473d757144e24188b458e57dc0e98943))
* **image:** url_decode strings ([d41704f](https://github.com/folke/snacks.nvim/commit/d41704f3daae823513c90adb913976bfabc36387))
* **image:** use `tectonic` when available ([8d073cc](https://github.com/folke/snacks.nvim/commit/8d073ccc0ca984f844cc2a8f8506f23f3fcea56a))
* **image:** use kitty's unicode placeholder images ([7d655fe](https://github.com/folke/snacks.nvim/commit/7d655fe09d2c705ff5707902f4ed925a62a61d3b))
* **image:** use search dirs to resolve file from both cwd and dirname of file. Closes [#1305](https://github.com/folke/snacks.nvim/issues/1305) ([bf01460](https://github.com/folke/snacks.nvim/commit/bf01460e6d82b720fb50664d372c1daf5ade249d))
* **image:** utility function to get a png dimensions from the file header ([a6d866a](https://github.com/folke/snacks.nvim/commit/a6d866ab72e5cad7840d69a7354cc67e2699f46e))
* **matcher:** call on_match after setting score ([23ce529](https://github.com/folke/snacks.nvim/commit/23ce529fb663337f9dc17ca08aa601b172469031))
* **picker.actions:** `cmd` action now always allows to edit the command. Closes [#1033](https://github.com/folke/snacks.nvim/issues/1033) ([a177885](https://github.com/folke/snacks.nvim/commit/a17788539a5e66784535d0c973bdc08728f16c46))
* **picker.actions:** option to disable notify for yank action. Closes [#1117](https://github.com/folke/snacks.nvim/issues/1117) ([f6a807d](https://github.com/folke/snacks.nvim/commit/f6a807da6d4e6ab591f85592a472bbb5bc6583f7))
* **picker.config:** better source field spec ([6c58b67](https://github.com/folke/snacks.nvim/commit/6c58b67890bbd2076a7f5b69f57ab666cb9b7410))
* **picker.db:** allow configuring the sqlite3 lib path. Closes [#1025](https://github.com/folke/snacks.nvim/issues/1025) ([b990044](https://github.com/folke/snacks.nvim/commit/b9900444d2ea494bba8857e5224059002ee8c465))
* **picker.files:** added `ft` option to filter by extension(s) ([12a7ea2](https://github.com/folke/snacks.nvim/commit/12a7ea28b97827575a1768d6013dd3c7bedd5ebb))
* **picker.format:** `opts.formatters.file.use_git_status_hl` defaults to `true` and adds git status hl to filename ([243eeca](https://github.com/folke/snacks.nvim/commit/243eecaca5f465602a9ba68e5c0fa375b90a13fb))
* **picker.git_diff:** use the `diff` previewer for `git_diff` so that `delta` can be used. See [#1302](https://github.com/folke/snacks.nvim/issues/1302) ([92786c5](https://github.com/folke/snacks.nvim/commit/92786c5b03ae4772521050acabcb619283eeb94a))
* **picker.git:** add confirmation before deleting a git branch ([#951](https://github.com/folke/snacks.nvim/issues/951)) ([337a3ae](https://github.com/folke/snacks.nvim/commit/337a3ae7eebb95020596f15a349a85d2f6be31a4))
* **picker.git:** add create and delete branch to git_branches ([#909](https://github.com/folke/snacks.nvim/issues/909)) ([8676c40](https://github.com/folke/snacks.nvim/commit/8676c409e148e28eff93c114aca0c1bf3d42281a))
* **picker.git:** allow passing extra args to git grep. Closes [#1184](https://github.com/folke/snacks.nvim/issues/1184) ([7122a03](https://github.com/folke/snacks.nvim/commit/7122a03fdf0b7bb9a5c6645b0e86f9e3a9f9290b))
* **picker.git:** allow passing extra args to other git pickers ([#1205](https://github.com/folke/snacks.nvim/issues/1205)) ([4d46574](https://github.com/folke/snacks.nvim/commit/4d46574b247d72bf1a602cdda2ddd8da39854234))
* **picker.lazy:** don't use `grep`. Parse spec files manually. Closes [#972](https://github.com/folke/snacks.nvim/issues/972) ([0928007](https://github.com/folke/snacks.nvim/commit/09280078e8339f018be5249fe0e1d7b9d32db7f7))
* **picker.lsp:** added original symbol to item.item. Closes [#1171](https://github.com/folke/snacks.nvim/issues/1171) ([45a6f8d](https://github.com/folke/snacks.nvim/commit/45a6f8d1ee0c323246413d1e1d43b0f0c9da18a2))
* **picker.lsp:** use existing buffers for preview when opened ([d4e6353](https://github.com/folke/snacks.nvim/commit/d4e63531c9fba63ded6fb470a5d53c98af110478))
* **picker.preview:** allow confguring `preview = {main = true, enabled = false}` ([1839c65](https://github.com/folke/snacks.nvim/commit/1839c65f6784bedb7ae96a84ee741fa5c0023226))
* **picker.preview:** allow passing additional args to the git preview command ([910437f](https://github.com/folke/snacks.nvim/commit/910437f1451ccaaa495aa1eca99e0a73fc798d40))
* **picker.proc:** added proc debug mode ([d870f16](https://github.com/folke/snacks.nvim/commit/d870f164534d1853fd8c599d7933cc5324272a09))
* **picker.undo:** `ctrl+y` to yank added lines, `ctrl+shift+y` to yank deleted lines ([3baf95d](https://github.com/folke/snacks.nvim/commit/3baf95d3a1005105b57ce53644ff6224ee3afa1c))
* **picker.undo:** added ctrl+y to yank added lines from undo ([811a24c](https://github.com/folke/snacks.nvim/commit/811a24cc16a8e9b7ec947c95b73e1fe05e4692d1))
* **picker.util:** lua globber ([97dcd9c](https://github.com/folke/snacks.nvim/commit/97dcd9c168c667538a4c6cc1384c4981a37afcad))
* **picker.util:** utility function to get all bins on the PATH ([5d42c7e](https://github.com/folke/snacks.nvim/commit/5d42c7e5e480bde04fd9506b3df64b579446c4f9))
* **picker:** `opts.focus` can be used to set default focus window. `opts.enter` if picker should be focused on enter.  Closes [#1162](https://github.com/folke/snacks.nvim/issues/1162) ([e8de28b](https://github.com/folke/snacks.nvim/commit/e8de28b56ec85ad45cdb3c303c5ee5da0e070baf))
* **picker:** add LSP symbol range to result item ([#1123](https://github.com/folke/snacks.nvim/issues/1123)) ([c0481ab](https://github.com/folke/snacks.nvim/commit/c0481ab0b69c6111bfc5077bd1550acbb480f05d))
* **picker:** added `c-q` to list ([6d0d2dc](https://github.com/folke/snacks.nvim/commit/6d0d2dc2a7e07de9704a172bd5295f4920eb965f))
* **picker:** added `git_grep` picker. Closes [#986](https://github.com/folke/snacks.nvim/issues/986) ([2dc9016](https://github.com/folke/snacks.nvim/commit/2dc901634b250059cc9b7129bdeeedd24520b86c))
* **picker:** added `lsp_config` source ([0d4aa98](https://github.com/folke/snacks.nvim/commit/0d4aa98cea0de6144853d820e52e6e35d0f0c609))
* **picker:** added treesitter symbols picker ([a6beb0f](https://github.com/folke/snacks.nvim/commit/a6beb0f280d3f43513998882faf199acf3818ddf))
* **picker:** allow complex titles ([#1112](https://github.com/folke/snacks.nvim/issues/1112)) ([f200b3f](https://github.com/folke/snacks.nvim/commit/f200b3f6c8f84147e1a80f70b8f1714645c59af6))
* **picker:** allow configuring file icon width. Closes [#981](https://github.com/folke/snacks.nvim/issues/981) ([52c1086](https://github.com/folke/snacks.nvim/commit/52c1086ecdf410dfec3317144d46de7c6f86c1ad))
* **picker:** allow overriding default file/dir/dir_open icons. Closes [#1199](https://github.com/folke/snacks.nvim/issues/1199) ([41c4391](https://github.com/folke/snacks.nvim/commit/41c4391b72ff5ef3dfa8216fc608bbe02bbd4d1c))
* **picker:** default `c-t` keymap to open in tab ([ffc6fe3](https://github.com/folke/snacks.nvim/commit/ffc6fe3965cb176c2b3e2bdb0aee4478e4dc2b94))
* **picker:** each window can now be `toggled` (also input), `hidden` and have `auto_hide` ([01efab2](https://github.com/folke/snacks.nvim/commit/01efab2ddb75d2077229231201c5a69ab2df3ad8))
* **picker:** get filetype from modeline when needed. Closes [#987](https://github.com/folke/snacks.nvim/issues/987) ([5af04ab](https://github.com/folke/snacks.nvim/commit/5af04ab6672ae38bf7d72427e75f925615f93904))
* **picker:** image previewer using kitty graphics protocol ([2b0aa93](https://github.com/folke/snacks.nvim/commit/2b0aa93efc9aa662e0cb9446cc4639f3be1a9d1e))
* **picker:** new native diff mode (disabled by default). Can be used to show delta diffs for undo. Closes [#1288](https://github.com/folke/snacks.nvim/issues/1288) ([d6a38ac](https://github.com/folke/snacks.nvim/commit/d6a38acbf5765eeb5ca2558bcb0d1ae1428dd2ca))
* **picker:** pin picker as a split to left/bottom/top/right with `ctrl+z+(hjkl)` ([27cba53](https://github.com/folke/snacks.nvim/commit/27cba535a6763cbca3f3162c5c4bb48c6f382005))
* **picker:** renamed `native` -&gt; `builtin` + fixed diff mode used for undo. Closes [#1302](https://github.com/folke/snacks.nvim/issues/1302) ([bd6a62a](https://github.com/folke/snacks.nvim/commit/bd6a62af12ca5e8cab88b94912e65bff26c9feba))
* **scope:** allow injected languages to be parsed by treesitter ([#823](https://github.com/folke/snacks.nvim/issues/823)) ([aba21dd](https://github.com/folke/snacks.nvim/commit/aba21ddc712b12db8469680dd7f2080063cb6d5c))
* **scroll:** big rework to make scroll play nice with virtual lines ([e71955a](https://github.com/folke/snacks.nvim/commit/e71955a941300cd81bf6d7ab36d1352b62d6f568))
* **scroll:** scroll improvements. Closes [#1024](https://github.com/folke/snacks.nvim/issues/1024) ([73d2f0f](https://github.com/folke/snacks.nvim/commit/73d2f0f40c702acaf7a1a3e833fc5460cb552578))
* **statuscolumn:** added mouse click handler to open/close folds. Closes [#968](https://github.com/folke/snacks.nvim/issues/968) ([98a7b64](https://github.com/folke/snacks.nvim/commit/98a7b647c9e245ef02d57d566bf8461c2f7beb56))
* **terminal:** added `Snacks.terminal.list()`. Closes [#421](https://github.com/folke/snacks.nvim/issues/421). Closes [#759](https://github.com/folke/snacks.nvim/issues/759) ([73c4b62](https://github.com/folke/snacks.nvim/commit/73c4b628963004760ccced0192d1c2633c9e3657))
* **terminal:** added `start_insert` ([64129e4](https://github.com/folke/snacks.nvim/commit/64129e4c3c5b247c61b1f46bc0faaa1e69e7eef8))
* **terminal:** auto_close and auto_insert. Closes [#965](https://github.com/folke/snacks.nvim/issues/965) ([bb76cae](https://github.com/folke/snacks.nvim/commit/bb76cae87e81a871435570b91c8c6f6e27eb9955))
* **terminal:** don't use deprecated `vim.fn.termopen` on Neovim &gt;= 0.10 ([37f6665](https://github.com/folke/snacks.nvim/commit/37f6665c488d90bf50b99cfe0b0fab40f990c497))
* test ([520ed85](https://github.com/folke/snacks.nvim/commit/520ed85169c873a8492077520ff37a5f0233c67d))
* **toggle:** allow user to add custom which-key description ([#1121](https://github.com/folke/snacks.nvim/issues/1121)) ([369732e](https://github.com/folke/snacks.nvim/commit/369732e65e0077e51487547131045526ccbdad1b))
* **treesitter:** add `tree` boolean to toggle on/off tree symbols ([#1105](https://github.com/folke/snacks.nvim/issues/1105)) ([c61f9eb](https://github.com/folke/snacks.nvim/commit/c61f9eb28695b9f96682d6dbf67072947dfa2737))
* **util:** `Snacks.util.winhl` helper to deal with `vim.wo.winhighlight` ([4c1d7b4](https://github.com/folke/snacks.nvim/commit/4c1d7b4720218122885877877e7883cc491133ed))
* **util:** base64 shim for Neovim &lt; 0.10 ([96f1227](https://github.com/folke/snacks.nvim/commit/96f12274a49bb2e6d0d558e652c728d27d4c3ff8))
* **util:** Snacks.util.color can now get the color from a list of hl groups ([a33f65d](https://github.com/folke/snacks.nvim/commit/a33f65d936a85efa9aaee9e44bcd70069134a816))
* **util:** util.spawn ([a76fe13](https://github.com/folke/snacks.nvim/commit/a76fe13148a899274484972a8705052bef4baa93))
* **words:** add `filter` function for user to disable specific filetypes ([#1296](https://github.com/folke/snacks.nvim/issues/1296)) ([d62e752](https://github.com/folke/snacks.nvim/commit/d62e7527a5e9608ab0033bc63a329baf8757ea6d))


### Bug Fixes

* **all:** better support for opening windows / pickers / ... on multiple tab pages. Closes [#1043](https://github.com/folke/snacks.nvim/issues/1043) ([8272c1c](https://github.com/folke/snacks.nvim/commit/8272c1c66f43390294debb24759c32627653aedb))
* **bigfile:** check that passed path is the one from the buffer ([8deea64](https://github.com/folke/snacks.nvim/commit/8deea64dba3b9b8f57e52bb6b0133263f6ff171f))
* **buffers:** use `"` mark for full buffer position when set. Closes [#1160](https://github.com/folke/snacks.nvim/issues/1160) ([7d350bc](https://github.com/folke/snacks.nvim/commit/7d350bc0c7e897ad0d7bd7fc9a470dabecab32ca))
* **compat:** correct Neovim 0.11 check ([448a55a](https://github.com/folke/snacks.nvim/commit/448a55a0e3c437bacc945c4ea98a6342ccb2b769))
* **dashboard:** allow dashboard to be the main editor window ([e3ead3c](https://github.com/folke/snacks.nvim/commit/e3ead3c648b3b6c8af0557c6412ae0307cc92018))
* **dashboard:** dashboard can be a main editor window ([f36c70a](https://github.com/folke/snacks.nvim/commit/f36c70a912c2893b10336b4645d3447264813a34))
* **dashboard:** use `Snacks.util.icon` for icons. Closes [#1192](https://github.com/folke/snacks.nvim/issues/1192) ([c2f06da](https://github.com/folke/snacks.nvim/commit/c2f06daeca6c3304d1e94225323dbe8c2f7f797d))
* **debug:** better args handling for debugging cmds ([48a3fed](https://github.com/folke/snacks.nvim/commit/48a3fed3c51390650d134bc5d76d15ace8d614ea))
* **explorer.git:** always at `.git` directory to ignored ([f7a35b8](https://github.com/folke/snacks.nvim/commit/f7a35b8214f393e2412adc0c8f2fe85d956c4b02))
* **explorer.git:** better git status watching ([09349ec](https://github.com/folke/snacks.nvim/commit/09349ecd44040666db9d4835994a378a9ff53e8c))
* **explorer.git:** dont reset cursor when git status is done updating ([bc87992](https://github.com/folke/snacks.nvim/commit/bc87992e712c29ef8e826f3550f9b8e3f1a9308d))
* **explorer.git:** vim.schedule git updates ([3aad761](https://github.com/folke/snacks.nvim/commit/3aad7616209951320d54f83dd7df35d5578ea61f))
* **explorer.tree:** fix linux ([6f5399b](https://github.com/folke/snacks.nvim/commit/6f5399b47c55f916fcc3a82dcc71cce0eb5d7c92))
* **explorer.tree:** symlink directories ([e5f1e91](https://github.com/folke/snacks.nvim/commit/e5f1e91249b468ff3a7d14a8650074c27f1fdb30))
* **explorer.watch:** pcall watcher, since it can give errors on windows ([af96818](https://github.com/folke/snacks.nvim/commit/af968181af6ce6a988765fe51558b2caefdcf863))
* **explorer:** always refresh state when opening the picker since changes might have happened that were not monitored ([c61114f](https://github.com/folke/snacks.nvim/commit/c61114fb32910863a543a4a7a1f63e9915983d26))
* **explorer:** call original `on_close`. Closes [#971](https://github.com/folke/snacks.nvim/issues/971) ([a0bee9f](https://github.com/folke/snacks.nvim/commit/a0bee9f662d4e22c6533e6544b4daedecd2aacc0))
* **explorer:** change grep in item dir keymap to leader-/. Closes [#1000](https://github.com/folke/snacks.nvim/issues/1000) ([9dfa276](https://github.com/folke/snacks.nvim/commit/9dfa276ea424a091f5d5bdc008aff127850441b2))
* **explorer:** check that picker is still open ([50fa1be](https://github.com/folke/snacks.nvim/commit/50fa1be38ee8366d79e1fa58b38abf31d3955033))
* **explorer:** disable follow for explorer search by default. No longer needed. Link directories may show as files then though, but that's not an issue. See [#960](https://github.com/folke/snacks.nvim/issues/960) ([b9a17d8](https://github.com/folke/snacks.nvim/commit/b9a17d82a726dc6cfd9a0b4f8566178708073808))
* **explorer:** dont focus first file when not searching ([3fd437c](https://github.com/folke/snacks.nvim/commit/3fd437ccd38d79b876154097149d130cdb01e653))
* **explorer:** dont process git when picker closed ([c255d9c](https://github.com/folke/snacks.nvim/commit/c255d9c6a02f070f0048c5eaa40921f71e9f2acb))
* **explorer:** last status for indent guides taking hidden / ignored files into account ([94bd2ef](https://github.com/folke/snacks.nvim/commit/94bd2eff74acd7faa78760bf8a55d9c269e99190))
* **explorer:** strip cwd from search text for explorer items ([38f392a](https://github.com/folke/snacks.nvim/commit/38f392a8ad75ced790f89c8ef43a91f98a2bb6e3))
* **explorer:** windows ([b560054](https://github.com/folke/snacks.nvim/commit/b56005466952b759a2f610e8b3c8263444402d76))
* **exporer.tree:** and now hopefully on windows ([ef9b12d](https://github.com/folke/snacks.nvim/commit/ef9b12d68010a931c76533925a8c730123241635))
* **gitbrowse:** add support for GitHub Enterprise Cloud repo url ([#1089](https://github.com/folke/snacks.nvim/issues/1089)) ([97fd57e](https://github.com/folke/snacks.nvim/commit/97fd57e8a0555023d2968354ca5f2b62de988103))
* **gitbrowse:** cwd for permalinks ([#1038](https://github.com/folke/snacks.nvim/issues/1038)) ([0bf47dc](https://github.com/folke/snacks.nvim/commit/0bf47dc319e4d6848366aff5c1a42cd08672d3e3))
* **gitbrowse:** previous logic always overwrote 'commit' ([#1127](https://github.com/folke/snacks.nvim/issues/1127)) ([2f3f080](https://github.com/folke/snacks.nvim/commit/2f3f080ede4d5f75c0b02d1698156648832cb974))
* **git:** use nul char as separator for git status ([8e0dfd2](https://github.com/folke/snacks.nvim/commit/8e0dfd285665bedf67441efe11c9c1318781826f))
* **health:** skip dot dirs... Closes [#1293](https://github.com/folke/snacks.nvim/issues/1293) ([aaed4a9](https://github.com/folke/snacks.nvim/commit/aaed4a94111ddfd9d23cdecb01e4ae53030c2c3e))
* **image.doc:** crop inline typst equations properly ([#1320](https://github.com/folke/snacks.nvim/issues/1320)) ([4f8b9eb](https://github.com/folke/snacks.nvim/commit/4f8b9ebf717b8acf41be02b0bd5a6d75f6038ea7))
* **image.doc:** fixed at_cursor. Closes [#1258](https://github.com/folke/snacks.nvim/issues/1258) ([76f5ee4](https://github.com/folke/snacks.nvim/commit/76f5ee4a1bd2566fc1460a1b11aa6a0bc36d2f5d))
* **image.health:** add check for ghost-script to render pdfs. Closes [#1248](https://github.com/folke/snacks.nvim/issues/1248) ([2b52d89](https://github.com/folke/snacks.nvim/commit/2b52d89508a448a1ca0c500afbd325e77023afc1))
* **image.health:** allow `convert` if `magick` not available ([4589e25](https://github.com/folke/snacks.nvim/commit/4589e2575894090a1e62aae11cf17856f5b84ea5))
* **image.hover:** close when needed. Closes [#1229](https://github.com/folke/snacks.nvim/issues/1229) ([1f9ba12](https://github.com/folke/snacks.nvim/commit/1f9ba127554bd3bd9780bfb925adfdf1e0ee73f9))
* **image.latex:** include doc packages for math rendering. Closes [#1262](https://github.com/folke/snacks.nvim/issues/1262) ([2ee6488](https://github.com/folke/snacks.nvim/commit/2ee64887c2be80c6b7b8fac4bb0617c827fde0d0))
* **image.latex:** inline math formulas. Closes [#1246](https://github.com/folke/snacks.nvim/issues/1246) ([9e422e1](https://github.com/folke/snacks.nvim/commit/9e422e12876002cba59ac4825bbeea89996e0196))
* **image.markdown:** fix image treesitter query. Closes [#1300](https://github.com/folke/snacks.nvim/issues/1300) ([830ac62](https://github.com/folke/snacks.nvim/commit/830ac62815e70f3db35ed7a295710c836578c6e3))
* **image.terminal:** set passthrough=all instead of on for tmux. Closes [#1249](https://github.com/folke/snacks.nvim/issues/1249) ([efcc25d](https://github.com/folke/snacks.nvim/commit/efcc25dcfa3ccaa7aa0a11b6cf065f8b8d32e485))
* **image:** added support for relative paths. Closes [#1143](https://github.com/folke/snacks.nvim/issues/1143) ([2ef6375](https://github.com/folke/snacks.nvim/commit/2ef63754b9c2a835a1d464766a22ba1bd4b16ea3))
* **image:** better cell size calculation for non-HDPI displays ([e146a66](https://github.com/folke/snacks.nvim/commit/e146a66cb767c60c6e84b2ab9a4522abdb6a5cc0))
* **image:** better image position caluclation. Closes [#1268](https://github.com/folke/snacks.nvim/issues/1268) ([5c0607e](https://github.com/folke/snacks.nvim/commit/5c0607e31a76317bc34f840fe8cc283b6a8d00c5))
* **image:** create cache dir ([f8c4e03](https://github.com/folke/snacks.nvim/commit/f8c4e03d025de17fb2302b3253bc72b8c0693c24))
* **image:** delay sending first image, to make ghostty happy. Closes [#1333](https://github.com/folke/snacks.nvim/issues/1333) ([9aa8cbb](https://github.com/folke/snacks.nvim/commit/9aa8cbb8031750fce640d476df67d88f60fd7c4e))
* **image:** delete terminal image on exit, just to be sure ([317bfac](https://github.com/folke/snacks.nvim/commit/317bfaca65dc53aa0a74885cf0c48c64fdfc30a9))
* **image:** do not attach to invalid buffers ([#1238](https://github.com/folke/snacks.nvim/issues/1238)) ([9a5e4de](https://github.com/folke/snacks.nvim/commit/9a5e4deaec451e77464759b4d78e7207caee14a7))
* **image:** don't fallback to `convert` on windows, since that is a system tool ([c1a1984](https://github.com/folke/snacks.nvim/commit/c1a1984fdb537017b6239d5592d1f7d25a77caa9))
* **image:** failed state ([5a37d83](https://github.com/folke/snacks.nvim/commit/5a37d838973f216822448a9dae935724754acbf0))
* **image:** fix disappearing images when changing colorscheme ([44e2f8e](https://github.com/folke/snacks.nvim/commit/44e2f8e573a8e4971badb8c7d3c1181fed7d5de3))
* **image:** fixed gsub for angle brackets. Closes [#1301](https://github.com/folke/snacks.nvim/issues/1301) ([beaa1c2](https://github.com/folke/snacks.nvim/commit/beaa1c2efcc598a9752b4536ef95606a10835aaa))
* **image:** fixup ([de3cba5](https://github.com/folke/snacks.nvim/commit/de3cba5158509b82e2f0ff9fc9101effccc1a863))
* **image:** handle file uppercase file extensions. Closes [#1202](https://github.com/folke/snacks.nvim/issues/1202) ([356f621](https://github.com/folke/snacks.nvim/commit/356f6216b90b85878af2c0134c8f3955349cae18))
* **image:** handle inline images at the same TS node, but that changed url. See [#1203](https://github.com/folke/snacks.nvim/issues/1203) ([86e3ddf](https://github.com/folke/snacks.nvim/commit/86e3ddf2e4f7a08d8172d2b2383eb51b4c0bbb5f))
* **image:** hide progress when finished loading in for wezterm ([526896a](https://github.com/folke/snacks.nvim/commit/526896ad3e736786c4520efce6f97c831677ca69))
* **image:** let text conversion continue on errors. See [#1303](https://github.com/folke/snacks.nvim/issues/1303) ([6d1cda4](https://github.com/folke/snacks.nvim/commit/6d1cda4a6df71f146daf171fbd6d95e53123a61e))
* **image:** mermaid theme. Closes [#1282](https://github.com/folke/snacks.nvim/issues/1282) ([8117fb4](https://github.com/folke/snacks.nvim/commit/8117fb4cbbaec9fbcfe7fe0b6c3a9c933d6c27ee))
* **image:** move assertion for src/content. See [#1276](https://github.com/folke/snacks.nvim/issues/1276) ([31e21cc](https://github.com/folke/snacks.nvim/commit/31e21ccef857e600a72fc059ee660fd134595f9d))
* **image:** only load image when the file exists. Closes [#1143](https://github.com/folke/snacks.nvim/issues/1143) ([298499d](https://github.com/folke/snacks.nvim/commit/298499dcb943ab49946e648ab79bf14868480560))
* **image:** only setup tmux pass-through on supported terminals. Fixes [#1054](https://github.com/folke/snacks.nvim/issues/1054) ([78e692c](https://github.com/folke/snacks.nvim/commit/78e692cd07b752e29e021635a70f353024d9c9b4))
* **image:** prevent image id collisions by interleaving the nvim pid hash in the image id ([31788ba](https://github.com/folke/snacks.nvim/commit/31788ba74e12081e79165f4447f6ff0f7e33b696))
* **image:** relax check for wezterm. Closes [#1076](https://github.com/folke/snacks.nvim/issues/1076) ([8d5ae25](https://github.com/folke/snacks.nvim/commit/8d5ae25806f88ec6c79f094eb7f3cc3413584309))
* **image:** remove `wezterm` from supported terminals, since they don't support unicode placeholders. Closes [#1063](https://github.com/folke/snacks.nvim/issues/1063) ([345260f](https://github.com/folke/snacks.nvim/commit/345260f39f70d63625e63d3c6771b2a8224f45c9))
* **image:** remove debug ([13863ea](https://github.com/folke/snacks.nvim/commit/13863ea25d169ef35f939b836c5edf8116042b89))
* **image:** remove ft check, since we use lang already. Closes [#1177](https://github.com/folke/snacks.nvim/issues/1177) ([4bcd26a](https://github.com/folke/snacks.nvim/commit/4bcd26aca8150a70b40b62673731046f85a205ff))
* **image:** remove some default latex packages ([f45dd6c](https://github.com/folke/snacks.nvim/commit/f45dd6c44c1319a2660b3b390d8d39ec5f2d73dc))
* **image:** remove test ([462578e](https://github.com/folke/snacks.nvim/commit/462578edb8fb13f0c158d2c9ac9479109dfdab31))
* **image:** return converted filename instead of original src. Closes [#1213](https://github.com/folke/snacks.nvim/issues/1213) ([118eab0](https://github.com/folke/snacks.nvim/commit/118eab0cfd4093bcd2b120378e5ea0685a333950))
* **image:** show full size when not showing image inline ([d7c8fd9](https://github.com/folke/snacks.nvim/commit/d7c8fd9a482a98e44442071d1d02342ebb256be4))
* **image:** support Neovim &lt; 0.10 ([c067ffe](https://github.com/folke/snacks.nvim/commit/c067ffe86ce931702f82d2a1bd4c0ea98c3bfdd0))
* **image:** wrong return when trying second command ([74c4298](https://github.com/folke/snacks.nvim/commit/74c42985be207f6c9ed164bd1fae6be81fecd5bb))
* **input:** add missing hl group for input title ([#1164](https://github.com/folke/snacks.nvim/issues/1164)) ([7014b91](https://github.com/folke/snacks.nvim/commit/7014b91b927a384a7219629cf53e19573c832c23))
* **layout:** deep merge instead of shallow merge for window options. Closes [#1166](https://github.com/folke/snacks.nvim/issues/1166) ([27256cf](https://github.com/folke/snacks.nvim/commit/27256cf989475e3305713341930a7709e3670eac))
* **layout:** just hide any layouts below a backdrop. easier and looks better. ([0dab071](https://github.com/folke/snacks.nvim/commit/0dab071dbabaea642f42b2a13d5fc8f00a391963))
* **layout:** make sure width/height are at least `1`. Closes [#1090](https://github.com/folke/snacks.nvim/issues/1090) ([c554097](https://github.com/folke/snacks.nvim/commit/c5540974fa7a55720f0a1e55d0afe948c8f8fe0a))
* **layout:** take winbar into account for split layouts. Closes [#996](https://github.com/folke/snacks.nvim/issues/996) ([e4e5040](https://github.com/folke/snacks.nvim/commit/e4e5040d9b9b58ac3bc44a6709bbb5e55e58adea))
* **layout:** zindex weirdness on stable. Closes [#1180](https://github.com/folke/snacks.nvim/issues/1180) ([72ffb3d](https://github.com/folke/snacks.nvim/commit/72ffb3d1a2812671bb3487e490a3b1dd380bc234))
* **notifier:** keep notif when current buf is notif buf ([a13c891](https://github.com/folke/snacks.nvim/commit/a13c891a59ec0e67a75824fe1505a9e57fbfca0f))
* **picker.actions:** better set cmdline. Closes [#1291](https://github.com/folke/snacks.nvim/issues/1291) ([570c035](https://github.com/folke/snacks.nvim/commit/570c035b9417aaa2f02cadf00c83f5b968a70b6c))
* **picker.actions:** check that plugin exists before loading it in help. Closes [#1134](https://github.com/folke/snacks.nvim/issues/1134) ([e326de9](https://github.com/folke/snacks.nvim/commit/e326de9e0ce4f97c974359568617dc69a0cd6d67))
* **picker.actions:** don't delete empty buffer when its in another tabpage. Closes [#1005](https://github.com/folke/snacks.nvim/issues/1005) ([1491b54](https://github.com/folke/snacks.nvim/commit/1491b543ef1d8a0eb29a6ebc35db4fb808dcb47f))
* **picker.actions:** don't reuse_win in floating windows (like the picker preview) ([4b9ea98](https://github.com/folke/snacks.nvim/commit/4b9ea98007cddc0af80fa0479a86a1bf2e880b66))
* **picker.actions:** fix qflist position ([#911](https://github.com/folke/snacks.nvim/issues/911)) ([6d3c135](https://github.com/folke/snacks.nvim/commit/6d3c1352358e0e2980f9f323b6ca8a62415963bc))
* **picker.actions:** keymap confirm. Closes [#1252](https://github.com/folke/snacks.nvim/issues/1252) ([a9a84dd](https://github.com/folke/snacks.nvim/commit/a9a84dde2e474eb9ee57630ab2f6418bfe1b380f))
* **picker.actions:** reverse prev/next on select with a reversed list layout. Closes [#1124](https://github.com/folke/snacks.nvim/issues/1124) ([eae55e7](https://github.com/folke/snacks.nvim/commit/eae55e7ca3b7c33882884a439e12d26200403a66))
* **picker.actions:** use `vim.v.register` instead of `+` as default. ([9ab6637](https://github.com/folke/snacks.nvim/commit/9ab6637df061fb03c6c5ba937dee5bfef92a6633))
* **picker.buffers:** remove `dd` to delete buffer from input keymaps. Closes [#1193](https://github.com/folke/snacks.nvim/issues/1193) ([f311d1c](https://github.com/folke/snacks.nvim/commit/f311d1c83a25fbce63e322c72ad6c99a02f84a2f))
* **picker.colorscheme:** use wildignore. Closes [#969](https://github.com/folke/snacks.nvim/issues/969) ([ba8badf](https://github.com/folke/snacks.nvim/commit/ba8badfe74783e97934c21a69e0c44883092587f))
* **picker.config:** use `&lt;c-w&gt;HJKL` to move float to far left/bottom/top/right. Only in normal mode. ([34dd83c](https://github.com/folke/snacks.nvim/commit/34dd83c2572658c3f6140e8a8acc1bcfbf7cf32b))
* **picker.explorer:** ensure diagnostics can be disabled ([#1145](https://github.com/folke/snacks.nvim/issues/1145)) ([885c140](https://github.com/folke/snacks.nvim/commit/885c1409e898b2f6f806cdb31f6ca9d7d84b4ff3))
* **picker.git:** account for deleted files in git diff. Closes [#1001](https://github.com/folke/snacks.nvim/issues/1001) ([e9e2e69](https://github.com/folke/snacks.nvim/commit/e9e2e6976e3cc7c1110892c9c4a6882dd88ca6fd))
* **picker.git:** apply args to `git`, and not `git grep`. ([2e284e2](https://github.com/folke/snacks.nvim/commit/2e284e23d956767a50321de9c9bb0c005ea7c51f))
* **picker.git:** better handling of multi file staging ([b39a3ba](https://github.com/folke/snacks.nvim/commit/b39a3ba40af7c63e0cf0f5e6a2c242c6d3f22591))
* **picker.git:** correct root dir for git log ([c114a0d](https://github.com/folke/snacks.nvim/commit/c114a0da1a3984345c3035474b8a688592288c9d))
* **picker.git:** formatting of git log ([f320026](https://github.com/folke/snacks.nvim/commit/f32002607a5a81a1d25eda27b954fc6ba8e9fd1b))
* **picker.git:** handle git status renames. Closes [#1003](https://github.com/folke/snacks.nvim/issues/1003) ([93ad23a](https://github.com/folke/snacks.nvim/commit/93ad23a0abb4c712722b74e3c066e6b42881fc81))
* **picker.git:** preserve chronological order when matching ([#1216](https://github.com/folke/snacks.nvim/issues/1216)) ([8b19fd0](https://github.com/folke/snacks.nvim/commit/8b19fd0332835d48f7fe9fe203fa1c2b27976cd2))
* **picker.git:** properly handle file renames for git log. Closes [#1154](https://github.com/folke/snacks.nvim/issues/1154) ([9c436cb](https://github.com/folke/snacks.nvim/commit/9c436cb273c9b6984da275ba449fda2780d4fa2e))
* **picker.help:** make sure plugin is loaded for which we want to view the help ([3841a87](https://github.com/folke/snacks.nvim/commit/3841a8705a5e433d88539176d7c67a0ee6a9a92c))
* **picker.highlight:** lower case treesitter parser name ([3367983](https://github.com/folke/snacks.nvim/commit/336798345c1503689917a4a4a03a03a3da33119a))
* **picker.highlights:** close on confirm. Closes [#1096](https://github.com/folke/snacks.nvim/issues/1096) ([76f6e4f](https://github.com/folke/snacks.nvim/commit/76f6e4f81cff6f00c8ff027af9351f38ffa6d9f0))
* **picker.input:** prevent save dialog ([fcb2f50](https://github.com/folke/snacks.nvim/commit/fcb2f508dd6b58c98b781229db895d22c69e6f21))
* **picker.lines:** use original buf instead of current (which can be the picker on refresh) ([7ccf9c9](https://github.com/folke/snacks.nvim/commit/7ccf9c9d6934a76d5bd835bbd6cf1e764960f14e))
* **picker.list:** `list:view` should never transform reverse. Closes [#1016](https://github.com/folke/snacks.nvim/issues/1016) ([be781f9](https://github.com/folke/snacks.nvim/commit/be781f9fcb3d99db03c9c6979386565b65f8801b))
* **picker.list:** allow horizontal scrolling in the list ([572436b](https://github.com/folke/snacks.nvim/commit/572436bc3f16691172a6a0e94c8ffaf16b4170f0))
* **picker.list:** better wrap settings for when wrapping is enabled ([a542ea4](https://github.com/folke/snacks.nvim/commit/a542ea4d3487bd1aa449350c320bfdbe0c23083b))
* **picker.list:** correct offset calculation for large scrolloff. Closes [#1208](https://github.com/folke/snacks.nvim/issues/1208) ([f4ca368](https://github.com/folke/snacks.nvim/commit/f4ca368672e2231cc34abbd96208812cc6bb1aa1))
* **picker.list:** don't return non-matching items. Closes [#1133](https://github.com/folke/snacks.nvim/issues/1133) ([d07e7ac](https://github.com/folke/snacks.nvim/commit/d07e7ac6209356f74405bdd9d881fcacdf80f5ad))
* **picker.list:** don't show preview when target cursor/top not yet reached. Closes [#1204](https://github.com/folke/snacks.nvim/issues/1204) ([b02cb5e](https://github.com/folke/snacks.nvim/commit/b02cb5e8826179b385b870edbda1631213391cf1))
* **picker.list:** dont transform with reverse for resolving target. Closes [#1142](https://github.com/folke/snacks.nvim/issues/1142) ([0e36317](https://github.com/folke/snacks.nvim/commit/0e363177bd4a8037a127bc3fab6bf9d442da1123))
* **picker.list:** keep existing target if it exists unless `force = true`. Closes [#1152](https://github.com/folke/snacks.nvim/issues/1152) ([121e74e](https://github.com/folke/snacks.nvim/commit/121e74e4a5b7962ee370a8d8ae75d1c7b4c2e11c))
* **picker.list:** let user override wrap ([22da4bd](https://github.com/folke/snacks.nvim/commit/22da4bd5118a63268e6516ac74a8c3dc514218d3))
* **picker.list:** reset preview when no results. Closes [#1133](https://github.com/folke/snacks.nvim/issues/1133) ([f8bc119](https://github.com/folke/snacks.nvim/commit/f8bc1192cb3f740913f9198fabaf87b46434a926))
* **picker.lsp:** fix indent guides for sorted document symbols ([17360e4](https://github.com/folke/snacks.nvim/commit/17360e400905f50c5cc513b072c207233f825a73))
* **picker.lsp:** handle invalid lsp kinds. Closes [#1182](https://github.com/folke/snacks.nvim/issues/1182) ([f3cdd02](https://github.com/folke/snacks.nvim/commit/f3cdd02620bd5075e453be7451a260dbbee68cab))
* **picker.lsp:** only sort when not getting workspace symbols. Closes [#1071](https://github.com/folke/snacks.nvim/issues/1071) ([d607d2e](https://github.com/folke/snacks.nvim/commit/d607d2e050d9ab19ebe51db38aab807958f05bad))
* **picker.lsp:** sort document symbols by position ([cc22177](https://github.com/folke/snacks.nvim/commit/cc22177dcf288195022b0f739da3d00fcf56e3d7))
* **picker.matcher:** don't optimize pattern subsets when pattern has a negation ([a6b3d78](https://github.com/folke/snacks.nvim/commit/a6b3d7840baef2cc9207353a7c1a782fc8508af9))
* **picker.matcher:** only consider subset patterns that contain only whitespace and alpha numeric chars. Closes [#1013](https://github.com/folke/snacks.nvim/issues/1013) ([fcf2311](https://github.com/folke/snacks.nvim/commit/fcf2311c0e68d91b71bc1be114ad13e84cd7771d))
* **picker.notifications:** close on confirm. Closes [#1092](https://github.com/folke/snacks.nvim/issues/1092) ([a8dda99](https://github.com/folke/snacks.nvim/commit/a8dda993e5f2a0262a2be1585511a6df7e5dcb8c))
* **picker.preview:** clear namespace on reset ([a6d418e](https://github.com/folke/snacks.nvim/commit/a6d418e877033de9a12288cdbf7e78d2f0f5d661))
* **picker.preview:** don't clear preview state on close so that colorscheme can be restored. Closes [#932](https://github.com/folke/snacks.nvim/issues/932) ([9688bd9](https://github.com/folke/snacks.nvim/commit/9688bd92cda4fbe57210bbdfbb9c940516382f9a))
* **picker.preview:** don't reset preview when filtering and the same item is previewed ([c8285c2](https://github.com/folke/snacks.nvim/commit/c8285c2ca2c4805019e105967f17e60f82faf106))
* **picker.preview:** fix newlines before setting lines of a buffer ([62c2c62](https://github.com/folke/snacks.nvim/commit/62c2c62671cf88ace1bd9fdd26411158d7072e0b))
* **picker.preview:** hide line numbers / status column for directory preview. Closes [#1029](https://github.com/folke/snacks.nvim/issues/1029) ([f9aca86](https://github.com/folke/snacks.nvim/commit/f9aca86bf3ddbbd56cb53f71250c301f90af35a2))
* **picker.preview:** preview for uris. Closes [#1075](https://github.com/folke/snacks.nvim/issues/1075) ([c1f93e2](https://github.com/folke/snacks.nvim/commit/c1f93e25bb927dce2e1eb46610b6347460f0c69b))
* **picker.preview:** update titles on layout update. Closes [#1113](https://github.com/folke/snacks.nvim/issues/1113) ([89b3ce1](https://github.com/folke/snacks.nvim/commit/89b3ce11ca700badc92af0e1a37be2e19b79cd55))
* **picker.preview:** work-around for Neovim's messy window-local options (that are never truly local). Closes [#1100](https://github.com/folke/snacks.nvim/issues/1100) ([e5960d8](https://github.com/folke/snacks.nvim/commit/e5960d8e32ed2771a1d84ce4532bf0e2dc4dc8ca))
* **picker.proc:** don't close stdout on proc exit, since it might still contain buffered output. Closes [#966](https://github.com/folke/snacks.nvim/issues/966) ([3b7021e](https://github.com/folke/snacks.nvim/commit/3b7021e7fdf88e13fdf06643ae9a7224e1291495))
* **picker.proc:** make sure to emit the last line when done. Closes [#1095](https://github.com/folke/snacks.nvim/issues/1095) ([b94926e](https://github.com/folke/snacks.nvim/commit/b94926e5cc697c54c73e7c2b3759c8432afca91d))
* **picker.projects:** add custom project dirs ([c7293bd](https://github.com/folke/snacks.nvim/commit/c7293bdfe7664eca6f49816795ffb7f2af5b8302))
* **picker.projects:** use fd or fdfind ([270250c](https://github.com/folke/snacks.nvim/commit/270250cf4646dbb16c3d1a453257a3f024b8f362))
* **picker.watch:** schedule_wrap. Closes [#1049](https://github.com/folke/snacks.nvim/issues/1049) ([f489d61](https://github.com/folke/snacks.nvim/commit/f489d61f54c3a32c35c439a16ff0f097dbe93028))
* **picker.zoxide:** directory icon ([#1031](https://github.com/folke/snacks.nvim/issues/1031)) ([33dbebb](https://github.com/folke/snacks.nvim/commit/33dbebb75395b5e80e441214985c0d9143d323d6))
* **picker:** `nil` on `:quit`. Closes [#1107](https://github.com/folke/snacks.nvim/issues/1107) ([1219f5e](https://github.com/folke/snacks.nvim/commit/1219f5e43baf1c17e305d605d3db8972aae19bf5))
* **picker:** `opts.focus = false` now works again ([031f9e9](https://github.com/folke/snacks.nvim/commit/031f9e96fb85cd417868ab2ba03946cb98fd06c8))
* **picker:** closed check for show preview. Closes [#1181](https://github.com/folke/snacks.nvim/issues/1181) ([c1f4d30](https://github.com/folke/snacks.nvim/commit/c1f4d3032529a7af3d0863c775841f6dc13e03d6))
* **picker:** consider zen windows as main. Closes [#973](https://github.com/folke/snacks.nvim/issues/973) ([b1db65a](https://github.com/folke/snacks.nvim/commit/b1db65ac61127581cbe3bca8e54a8faf8ce16e5f))
* **picker:** disabled preview main ([9fe43bd](https://github.com/folke/snacks.nvim/commit/9fe43bdf9b6c04b129e84bd7c2cb7ebd8e04bfae))
* **picker:** don't render list when closed. See [#1308](https://github.com/folke/snacks.nvim/issues/1308) ([681ae6e](https://github.com/folke/snacks.nvim/commit/681ae6e3078503d2c4cc137a492782a0ee3977b3))
* **picker:** exit insert mode before closing with `&lt;c-c&gt;` to prevent cursor shifting left. Close [#956](https://github.com/folke/snacks.nvim/issues/956) ([71eae96](https://github.com/folke/snacks.nvim/commit/71eae96bfa5ccafad9966a7bc40982ebe05d8f5d))
* **picker:** go back to last window on cancel instead of main ([4551f49](https://github.com/folke/snacks.nvim/commit/4551f499c7945036761fd48927cc07b9720fce56))
* **picker:** initial preview state when main ([cd6e336](https://github.com/folke/snacks.nvim/commit/cd6e336ec0dc8b95e7a75c86cba297a16929370e))
* **picker:** only show extmark errors when debug is enabled. Closes [#988](https://github.com/folke/snacks.nvim/issues/988) ([f6d9af7](https://github.com/folke/snacks.nvim/commit/f6d9af7410963780c48772f7bd9ee3f5e7be8599))
* **picker:** remove debug ([a23b10e](https://github.com/folke/snacks.nvim/commit/a23b10e6cafeae7b9e06be47ba49295d0c921a97))
* **picker:** remove debug :) ([3d53a73](https://github.com/folke/snacks.nvim/commit/3d53a7364e438a7652bb6b90b95c334c32cab938))
* **picker:** save toggles for resume. Closes [#1085](https://github.com/folke/snacks.nvim/issues/1085) ([e390713](https://github.com/folke/snacks.nvim/commit/e390713ac6f92d0076f38b518645b55222ecf4d1))
* **picker:** sometimes main layout win gets selected. Closes [#1015](https://github.com/folke/snacks.nvim/issues/1015) ([4799f82](https://github.com/folke/snacks.nvim/commit/4799f829683272b06ad9bf8b8e9816f28b3a46ef))
* **picker:** update titles last on show. Closes [#1113](https://github.com/folke/snacks.nvim/issues/1113) ([96796db](https://github.com/folke/snacks.nvim/commit/96796db21e474eff0d0ddeee2afa6c2c346756c7))
* **picker:** vim.ui.select callback is called when canceling selection ([#1115](https://github.com/folke/snacks.nvim/issues/1115)) ([4c3bfa2](https://github.com/folke/snacks.nvim/commit/4c3bfa29f3122c4fb855c1adaef01cf22612624a))
* **scroll:** added `keepjumps` ([7161dc1](https://github.com/folke/snacks.nvim/commit/7161dc1b570849324bb2b0b808c6f2cc46ef6f84))
* **statuscolumn:** only execute `za` when fold exists ([#1093](https://github.com/folke/snacks.nvim/issues/1093)) ([345eeb6](https://github.com/folke/snacks.nvim/commit/345eeb69417f5568930f283e3be01f0ef55bee63))
* **terminal:** check for 0.11 ([6e45829](https://github.com/folke/snacks.nvim/commit/6e45829879da987cb4ed01d3098eb2507da72343))
* **terminal:** softer check for using jobstart with `term=true` instead of deprecated termopen ([544a2ae](https://github.com/folke/snacks.nvim/commit/544a2ae01c28056629a0c90f8d0ff40995c84e42))
* **toggle:** hide toggle when real keymap does not exist. Closes [#378](https://github.com/folke/snacks.nvim/issues/378) ([ee9e617](https://github.com/folke/snacks.nvim/commit/ee9e6179fe18a2bf36ebb5e81ddf1052e04577dc))
* **win:** apply win-local window options for new buffers displayed in a window. Fixes [#925](https://github.com/folke/snacks.nvim/issues/925) ([cb99c46](https://github.com/folke/snacks.nvim/commit/cb99c46fa171134f582f6b13bef32f6d25ebda59))
* **win:** better handling when the command window is open. Closes [#1245](https://github.com/folke/snacks.nvim/issues/1245) ([7720410](https://github.com/folke/snacks.nvim/commit/77204102a1f5869bf37d8ccbc5e8e0769cfe8db4))
* **win:** call `on_close` before actually closing so that prev win can be set. Closes [#962](https://github.com/folke/snacks.nvim/issues/962) ([a1cb54c](https://github.com/folke/snacks.nvim/commit/a1cb54cc9e579c53bbd4b96949acf2341b31a3ee))
* **words:** default count to 1. Closes [#1307](https://github.com/folke/snacks.nvim/issues/1307) ([45ec90b](https://github.com/folke/snacks.nvim/commit/45ec90bdd91d7730b81662ee3bfcdd4a88ed908f))
* **zen:** properly get zoom options. Closes [#1207](https://github.com/folke/snacks.nvim/issues/1207) ([3100333](https://github.com/folke/snacks.nvim/commit/3100333fdb777853c77aeac46b92fcdaba8e3e57))


### Performance Improvements

* **dashboard:** speed up filtering for recent_files ([#1250](https://github.com/folke/snacks.nvim/issues/1250)) ([b91f417](https://github.com/folke/snacks.nvim/commit/b91f417670e8f35ac96a2ebdecceeafdcc43ba4a))
* **explorer:** disable watchdirs fallback watcher ([5d34380](https://github.com/folke/snacks.nvim/commit/5d34380310861cd42e32ce0865bd8cded9027b41))
* **explorer:** early exit for tree calculation ([1a30610](https://github.com/folke/snacks.nvim/commit/1a30610ab78cce8bb184166de2ef35ee2ca1987a))
* **explorer:** no need to get git status with `-uall`. Closes [#983](https://github.com/folke/snacks.nvim/issues/983) ([bc087d3](https://github.com/folke/snacks.nvim/commit/bc087d36d6126ccf25f8bb3ead405ec32547d85d))
* **explorer:** only update tree if git status actually changed ([5a2acf8](https://github.com/folke/snacks.nvim/commit/5a2acf82b2aff0b6f7121ce953c5754de6fd1e01))
* **explorer:** only update tree when diagnostics actually changed ([1142f46](https://github.com/folke/snacks.nvim/commit/1142f46a27358c8f48023382389a8b31c9628b6b))
* **image.convert:** identify during convert instead of calling identify afterwards ([7b7f42f](https://github.com/folke/snacks.nvim/commit/7b7f42fb3bee6083677d66b301424c26b4ff41c2))
* **image:** no need to run identify before convert for local files ([e2d9941](https://github.com/folke/snacks.nvim/commit/e2d99418968b0dc690ca6b56dac688d70e9b5e40))
* **picker.list:** only re-render when visible items changed ([c72e62e](https://github.com/folke/snacks.nvim/commit/c72e62ef9012161ec6cd86aa749d780f77d1cc87))
* **picker:** cache treesitter line highlights ([af31c31](https://github.com/folke/snacks.nvim/commit/af31c312872cab2a47e17ed2ee67bf5940a522d4))
* **picker:** cache wether ts lang exists and disable smooth scrolling on big files ([719b36f](https://github.com/folke/snacks.nvim/commit/719b36fa70c35a7015537aa0bfd2956f6128c87d))
* **scroll:** much better/easier/faster method for vertical cursor positioning ([a3194d9](https://github.com/folke/snacks.nvim/commit/a3194d95199c4699a4da0d4c425a19544ed8d670))


### Documentation

* docgen ([b503e3e](https://github.com/folke/snacks.nvim/commit/b503e3ee9fdd57202e5815747e67d1f6259468a4))

## [2.20.0](https://github.com/folke/snacks.nvim/compare/v2.19.0...v2.20.0) (2025-02-08)


### Features

* **picker.undo:** `ctrl+y` to yank added lines, `ctrl+shift+y` to yank deleted lines ([3baf95d](https://github.com/folke/snacks.nvim/commit/3baf95d3a1005105b57ce53644ff6224ee3afa1c))
* **picker:** added treesitter symbols picker ([a6beb0f](https://github.com/folke/snacks.nvim/commit/a6beb0f280d3f43513998882faf199acf3818ddf))
* **terminal:** added `Snacks.terminal.list()`. Closes [#421](https://github.com/folke/snacks.nvim/issues/421). Closes [#759](https://github.com/folke/snacks.nvim/issues/759) ([73c4b62](https://github.com/folke/snacks.nvim/commit/73c4b628963004760ccced0192d1c2633c9e3657))


### Bug Fixes

* **explorer:** change grep in item dir keymap to leader-/. Closes [#1000](https://github.com/folke/snacks.nvim/issues/1000) ([9dfa276](https://github.com/folke/snacks.nvim/commit/9dfa276ea424a091f5d5bdc008aff127850441b2))
* **layout:** take winbar into account for split layouts. Closes [#996](https://github.com/folke/snacks.nvim/issues/996) ([e4e5040](https://github.com/folke/snacks.nvim/commit/e4e5040d9b9b58ac3bc44a6709bbb5e55e58adea))
* **picker.git:** account for deleted files in git diff. Closes [#1001](https://github.com/folke/snacks.nvim/issues/1001) ([e9e2e69](https://github.com/folke/snacks.nvim/commit/e9e2e6976e3cc7c1110892c9c4a6882dd88ca6fd))
* **picker.git:** handle git status renames. Closes [#1003](https://github.com/folke/snacks.nvim/issues/1003) ([93ad23a](https://github.com/folke/snacks.nvim/commit/93ad23a0abb4c712722b74e3c066e6b42881fc81))
* **picker.lines:** use original buf instead of current (which can be the picker on refresh) ([7ccf9c9](https://github.com/folke/snacks.nvim/commit/7ccf9c9d6934a76d5bd835bbd6cf1e764960f14e))
* **picker.proc:** don't close stdout on proc exit, since it might still contain buffered output. Closes [#966](https://github.com/folke/snacks.nvim/issues/966) ([3b7021e](https://github.com/folke/snacks.nvim/commit/3b7021e7fdf88e13fdf06643ae9a7224e1291495))

## [2.19.0](https://github.com/folke/snacks.nvim/compare/v2.18.0...v2.19.0) (2025-02-07)


### Features

* **bigfile:** configurable average line length (default = 1000). Useful for minified files. Closes [#576](https://github.com/folke/snacks.nvim/issues/576). Closes [#372](https://github.com/folke/snacks.nvim/issues/372) ([7fa92a2](https://github.com/folke/snacks.nvim/commit/7fa92a24501fa85b567c130b4e026f9ca1efed17))
* **explorer:** add hl groups for ignored / hidden files. Closes [#887](https://github.com/folke/snacks.nvim/issues/887) ([85e1b34](https://github.com/folke/snacks.nvim/commit/85e1b343b0cc6d2facc0763d9e1c1de4b63b99ac))
* **explorer:** added ctrl+f to grep in the item's directory ([0454b21](https://github.com/folke/snacks.nvim/commit/0454b21165cb84d2f59a1daf6226de065c90d4f7))
* **explorer:** added ctrl+t to open a terminal in the item's directory ([81f9006](https://github.com/folke/snacks.nvim/commit/81f90062c50430c1bad9546fcb65c3e43a76be9b))
* **explorer:** added diagnostics file/directory status ([7f1b60d](https://github.com/folke/snacks.nvim/commit/7f1b60d5576345af5e7b990f3a9e4bca49cd3686))
* **explorer:** added quick nav with `[`, `]` with `d/w/e` for diagnostics ([d1d5585](https://github.com/folke/snacks.nvim/commit/d1d55850ecb4aac1396c314a159db1e90a34bd79))
* **explorer:** added support for live search ([82c4a50](https://github.com/folke/snacks.nvim/commit/82c4a50985c9bb9f4b1d598f10a30e1122a35212))
* **explorer:** allow disabling untracked git status. Closes [#983](https://github.com/folke/snacks.nvim/issues/983) ([a3b083b](https://github.com/folke/snacks.nvim/commit/a3b083b8443b1ae1299747fdac8da51c3160835b))
* **explorer:** different hl group for broken links ([1989921](https://github.com/folke/snacks.nvim/commit/1989921466e6b5234ae8f71add41b8defd55f732))
* **explorer:** disable fuzzy searches by default for explorer since it's too noisy and we can't sort on score due to tree view ([b07788f](https://github.com/folke/snacks.nvim/commit/b07788f14a28daa8d0b387c1258f8f348f47420f))
* **explorer:** file watcher when explorer is open ([6936c14](https://github.com/folke/snacks.nvim/commit/6936c1491d4aa8ffb4448acca677589a1472bb3a))
* **explorer:** file watching that works on all platforms ([8399465](https://github.com/folke/snacks.nvim/commit/8399465872c51fab54ad5d02eb315e258ec96ed1))
* **explorer:** focus on first file when searching in the explorer ([1d4bea4](https://github.com/folke/snacks.nvim/commit/1d4bea4a9ee8a5258c6ae085ac66dd5cc05a9749))
* **explorer:** git index watcher ([4c12475](https://github.com/folke/snacks.nvim/commit/4c12475e80528d8d48b9584d78d645e4a51c3298))
* **explorer:** rewrite that no longer depends on `fd` for exploring ([6149a7b](https://github.com/folke/snacks.nvim/commit/6149a7babbd2c6d9cd924bb70102d80a7f045287))
* **explorer:** show symlink target ([dfa79e0](https://github.com/folke/snacks.nvim/commit/dfa79e04436ebfdc83ba71c0048fc1636b4de5aa))
* **matcher:** call on_match after setting score ([23ce529](https://github.com/folke/snacks.nvim/commit/23ce529fb663337f9dc17ca08aa601b172469031))
* **picker.git:** add confirmation before deleting a git branch ([#951](https://github.com/folke/snacks.nvim/issues/951)) ([337a3ae](https://github.com/folke/snacks.nvim/commit/337a3ae7eebb95020596f15a349a85d2f6be31a4))
* **picker.git:** add create and delete branch to git_branches ([#909](https://github.com/folke/snacks.nvim/issues/909)) ([8676c40](https://github.com/folke/snacks.nvim/commit/8676c409e148e28eff93c114aca0c1bf3d42281a))
* **picker.lazy:** don't use `grep`. Parse spec files manually. Closes [#972](https://github.com/folke/snacks.nvim/issues/972) ([0928007](https://github.com/folke/snacks.nvim/commit/09280078e8339f018be5249fe0e1d7b9d32db7f7))
* **picker.lsp:** use existing buffers for preview when opened ([d4e6353](https://github.com/folke/snacks.nvim/commit/d4e63531c9fba63ded6fb470a5d53c98af110478))
* **picker.matcher:** internal `on_match` ([47b3b69](https://github.com/folke/snacks.nvim/commit/47b3b69570271b12bbd72b9dbcfbd445b915beca))
* **picker.preview:** allow confguring `preview = {main = true, enabled = false}` ([1839c65](https://github.com/folke/snacks.nvim/commit/1839c65f6784bedb7ae96a84ee741fa5c0023226))
* **picker.undo:** added ctrl+y to yank added lines from undo ([811a24c](https://github.com/folke/snacks.nvim/commit/811a24cc16a8e9b7ec947c95b73e1fe05e4692d1))
* **picker:** `picker:dir()` to get the dir of the item (when a directory) or it's parent (when a file) ([969608a](https://github.com/folke/snacks.nvim/commit/969608ab795718cc23299247bf4ea0a199fcca3f))
* **picker:** added `git_grep` picker. Closes [#986](https://github.com/folke/snacks.nvim/issues/986) ([2dc9016](https://github.com/folke/snacks.nvim/commit/2dc901634b250059cc9b7129bdeeedd24520b86c))
* **picker:** allow configuring file icon width. Closes [#981](https://github.com/folke/snacks.nvim/issues/981) ([52c1086](https://github.com/folke/snacks.nvim/commit/52c1086ecdf410dfec3317144d46de7c6f86c1ad))
* **picker:** better checkhealth ([b773368](https://github.com/folke/snacks.nvim/commit/b773368f8aa6e84a68e979f0e335d23de71f405a))
* **picker:** get filetype from modeline when needed. Closes [#987](https://github.com/folke/snacks.nvim/issues/987) ([5af04ab](https://github.com/folke/snacks.nvim/commit/5af04ab6672ae38bf7d72427e75f925615f93904))
* **picker:** opts.on_close ([6235f44](https://github.com/folke/snacks.nvim/commit/6235f44b115c45dd009c45b81a52f8d99863efaa))
* **picker:** pin picker as a split to left/bottom/top/right with `ctrl+z+(hjkl)` ([27cba53](https://github.com/folke/snacks.nvim/commit/27cba535a6763cbca3f3162c5c4bb48c6f382005))
* **rename:** add `old` to `on_rename` callback ([455228e](https://github.com/folke/snacks.nvim/commit/455228ed3a07bf3aee34a75910785b9978f53da6))
* **scope:** allow injected languages to be parsed by treesitter ([#823](https://github.com/folke/snacks.nvim/issues/823)) ([aba21dd](https://github.com/folke/snacks.nvim/commit/aba21ddc712b12db8469680dd7f2080063cb6d5c))
* **statuscolumn:** added mouse click handler to open/close folds. Closes [#968](https://github.com/folke/snacks.nvim/issues/968) ([98a7b64](https://github.com/folke/snacks.nvim/commit/98a7b647c9e245ef02d57d566bf8461c2f7beb56))
* **terminal:** added `start_insert` ([64129e4](https://github.com/folke/snacks.nvim/commit/64129e4c3c5b247c61b1f46bc0faaa1e69e7eef8))
* **terminal:** auto_close and auto_insert. Closes [#965](https://github.com/folke/snacks.nvim/issues/965) ([bb76cae](https://github.com/folke/snacks.nvim/commit/bb76cae87e81a871435570b91c8c6f6e27eb9955))


### Bug Fixes

* **bigfile:** check that passed path is the one from the buffer ([8deea64](https://github.com/folke/snacks.nvim/commit/8deea64dba3b9b8f57e52bb6b0133263f6ff171f))
* **explorer.git:** better git status watching ([09349ec](https://github.com/folke/snacks.nvim/commit/09349ecd44040666db9d4835994a378a9ff53e8c))
* **explorer.git:** dont reset cursor when git status is done updating ([bc87992](https://github.com/folke/snacks.nvim/commit/bc87992e712c29ef8e826f3550f9b8e3f1a9308d))
* **explorer.git:** vim.schedule git updates ([3aad761](https://github.com/folke/snacks.nvim/commit/3aad7616209951320d54f83dd7df35d5578ea61f))
* **explorer.tree:** fix linux ([6f5399b](https://github.com/folke/snacks.nvim/commit/6f5399b47c55f916fcc3a82dcc71cce0eb5d7c92))
* **explorer.tree:** symlink directories ([e5f1e91](https://github.com/folke/snacks.nvim/commit/e5f1e91249b468ff3a7d14a8650074c27f1fdb30))
* **explorer.watch:** pcall watcher, since it can give errors on windows ([af96818](https://github.com/folke/snacks.nvim/commit/af968181af6ce6a988765fe51558b2caefdcf863))
* **explorer:** always refresh state when opening the picker since changes might have happened that were not monitored ([c61114f](https://github.com/folke/snacks.nvim/commit/c61114fb32910863a543a4a7a1f63e9915983d26))
* **explorer:** call original `on_close`. Closes [#971](https://github.com/folke/snacks.nvim/issues/971) ([a0bee9f](https://github.com/folke/snacks.nvim/commit/a0bee9f662d4e22c6533e6544b4daedecd2aacc0))
* **explorer:** check that picker is still open ([50fa1be](https://github.com/folke/snacks.nvim/commit/50fa1be38ee8366d79e1fa58b38abf31d3955033))
* **explorer:** clear cache after action. Fixes [#890](https://github.com/folke/snacks.nvim/issues/890) ([34097ff](https://github.com/folke/snacks.nvim/commit/34097ff37e0fb53771bbe3bf927048d06b4576f6))
* **explorer:** clear selection after delete. Closes [#898](https://github.com/folke/snacks.nvim/issues/898) ([44733ea](https://github.com/folke/snacks.nvim/commit/44733eaa78fb899dc3b3d72d7cac6f447356a287))
* **explorer:** disable follow for explorer search by default. No longer needed. Link directories may show as files then though, but that's not an issue. See [#960](https://github.com/folke/snacks.nvim/issues/960) ([b9a17d8](https://github.com/folke/snacks.nvim/commit/b9a17d82a726dc6cfd9a0b4f8566178708073808))
* **explorer:** don't use --absolute-path option, since that resolves paths to realpath. See [#901](https://github.com/folke/snacks.nvim/issues/901). See [#905](https://github.com/folke/snacks.nvim/issues/905). See [#904](https://github.com/folke/snacks.nvim/issues/904) ([97570d2](https://github.com/folke/snacks.nvim/commit/97570d23ac42dac813c5eb49637381fa3b728246))
* **explorer:** dont focus first file when not searching ([3fd437c](https://github.com/folke/snacks.nvim/commit/3fd437ccd38d79b876154097149d130cdb01e653))
* **explorer:** dont process git when picker closed ([c255d9c](https://github.com/folke/snacks.nvim/commit/c255d9c6a02f070f0048c5eaa40921f71e9f2acb))
* **explorer:** last status for indent guides taking hidden / ignored files into account ([94bd2ef](https://github.com/folke/snacks.nvim/commit/94bd2eff74acd7faa78760bf8a55d9c269e99190))
* **explorer:** strip cwd from search text for explorer items ([38f392a](https://github.com/folke/snacks.nvim/commit/38f392a8ad75ced790f89c8ef43a91f98a2bb6e3))
* **explorer:** windows ([b560054](https://github.com/folke/snacks.nvim/commit/b56005466952b759a2f610e8b3c8263444402d76))
* **exporer.tree:** and now hopefully on windows ([ef9b12d](https://github.com/folke/snacks.nvim/commit/ef9b12d68010a931c76533925a8c730123241635))
* **git:** use nul char as separator for git status ([8e0dfd2](https://github.com/folke/snacks.nvim/commit/8e0dfd285665bedf67441efe11c9c1318781826f))
* **health:** better health checks for picker. Closes [#917](https://github.com/folke/snacks.nvim/issues/917) ([427f036](https://github.com/folke/snacks.nvim/commit/427f036c6532097859d9177f32ccb037803abaa4))
* **picker.actions:** close preview before buffer delete ([762821e](https://github.com/folke/snacks.nvim/commit/762821e420cef56b03b6897b008454eefe68fd1d))
* **picker.actions:** don't reuse_win in floating windows (like the picker preview) ([4b9ea98](https://github.com/folke/snacks.nvim/commit/4b9ea98007cddc0af80fa0479a86a1bf2e880b66))
* **picker.actions:** fix qflist position ([#911](https://github.com/folke/snacks.nvim/issues/911)) ([6d3c135](https://github.com/folke/snacks.nvim/commit/6d3c1352358e0e2980f9f323b6ca8a62415963bc))
* **picker.actions:** get win after splitting or tabnew. Fixes [#896](https://github.com/folke/snacks.nvim/issues/896) ([95d3e7f](https://github.com/folke/snacks.nvim/commit/95d3e7f96182e4cc8aa0c05a616a61eba9a77366))
* **picker.colorscheme:** use wildignore. Closes [#969](https://github.com/folke/snacks.nvim/issues/969) ([ba8badf](https://github.com/folke/snacks.nvim/commit/ba8badfe74783e97934c21a69e0c44883092587f))
* **picker.db:** better script to download sqlite3 on windows. See [#918](https://github.com/folke/snacks.nvim/issues/918) ([84d1a92](https://github.com/folke/snacks.nvim/commit/84d1a92faba55a470a6c52a1aca86988b0c57869))
* **picker.finder:** correct check if filter changed ([52bc24c](https://github.com/folke/snacks.nvim/commit/52bc24c23256246e863992dfcc3172c527254f55))
* **picker.input:** fixed startinsert weirdness with prompt buffers (again) ([c030827](https://github.com/folke/snacks.nvim/commit/c030827d7ad3fe7117bf81c0db1613c958015211))
* **picker.input:** set as not modified when setting input through API ([54a041f](https://github.com/folke/snacks.nvim/commit/54a041f7fca05234379d7bceff6b036acc679cdc))
* **picker.list:** better wrap settings for when wrapping is enabled ([a542ea4](https://github.com/folke/snacks.nvim/commit/a542ea4d3487bd1aa449350c320bfdbe0c23083b))
* **picker.list:** let user override wrap ([22da4bd](https://github.com/folke/snacks.nvim/commit/22da4bd5118a63268e6516ac74a8c3dc514218d3))
* **picker.list:** nil check ([c22e46a](https://github.com/folke/snacks.nvim/commit/c22e46ab9a1f1416368759e0979bc5c0c64c0084))
* **picker.lsp:** fix indent guides for sorted document symbols ([17360e4](https://github.com/folke/snacks.nvim/commit/17360e400905f50c5cc513b072c207233f825a73))
* **picker.lsp:** sort document symbols by position ([cc22177](https://github.com/folke/snacks.nvim/commit/cc22177dcf288195022b0f739da3d00fcf56e3d7))
* **picker.matcher:** don't optimize pattern subsets when pattern has a negation ([a6b3d78](https://github.com/folke/snacks.nvim/commit/a6b3d7840baef2cc9207353a7c1a782fc8508af9))
* **picker.preview:** don't clear preview state on close so that colorscheme can be restored. Closes [#932](https://github.com/folke/snacks.nvim/issues/932) ([9688bd9](https://github.com/folke/snacks.nvim/commit/9688bd92cda4fbe57210bbdfbb9c940516382f9a))
* **picker.preview:** update main preview when changing the layout ([604c603](https://github.com/folke/snacks.nvim/commit/604c603dfafdac0c2edc725ff8bcdcc395100028))
* **picker.projects:** add custom project dirs ([c7293bd](https://github.com/folke/snacks.nvim/commit/c7293bdfe7664eca6f49816795ffb7f2af5b8302))
* **picker.projects:** use fd or fdfind ([270250c](https://github.com/folke/snacks.nvim/commit/270250cf4646dbb16c3d1a453257a3f024b8f362))
* **picker.select:** default height shows just the items. See [#902](https://github.com/folke/snacks.nvim/issues/902) ([c667622](https://github.com/folke/snacks.nvim/commit/c667622fb7569a020195e3e35c1405e4a1fa0e7e))
* **picker:** better handling when entering the root layout window. Closes [#894](https://github.com/folke/snacks.nvim/issues/894) ([e294fd8](https://github.com/folke/snacks.nvim/commit/e294fd8a273b1f5622c8a3259802d91a1ed01110))
* **picker:** consider zen windows as main. Closes [#973](https://github.com/folke/snacks.nvim/issues/973) ([b1db65a](https://github.com/folke/snacks.nvim/commit/b1db65ac61127581cbe3bca8e54a8faf8ce16e5f))
* **picker:** disabled preview main ([9fe43bd](https://github.com/folke/snacks.nvim/commit/9fe43bdf9b6c04b129e84bd7c2cb7ebd8e04bfae))
* **picker:** exit insert mode before closing with `&lt;c-c&gt;` to prevent cursor shifting left. Close [#956](https://github.com/folke/snacks.nvim/issues/956) ([71eae96](https://github.com/folke/snacks.nvim/commit/71eae96bfa5ccafad9966a7bc40982ebe05d8f5d))
* **picker:** initial preview state when main ([cd6e336](https://github.com/folke/snacks.nvim/commit/cd6e336ec0dc8b95e7a75c86cba297a16929370e))
* **picker:** only show extmark errors when debug is enabled. Closes [#988](https://github.com/folke/snacks.nvim/issues/988) ([f6d9af7](https://github.com/folke/snacks.nvim/commit/f6d9af7410963780c48772f7bd9ee3f5e7be8599))
* **win:** apply win-local window options for new buffers displayed in a window. Fixes [#925](https://github.com/folke/snacks.nvim/issues/925) ([cb99c46](https://github.com/folke/snacks.nvim/commit/cb99c46fa171134f582f6b13bef32f6d25ebda59))
* **win:** better handling of alien buffers opening in managed windows. See [#886](https://github.com/folke/snacks.nvim/issues/886) ([c8430fd](https://github.com/folke/snacks.nvim/commit/c8430fdd8dec6aa21c73f293cbd363084fb56ab0))


### Performance Improvements

* **explorer:** disable watchdirs fallback watcher ([5d34380](https://github.com/folke/snacks.nvim/commit/5d34380310861cd42e32ce0865bd8cded9027b41))
* **explorer:** early exit for tree calculation ([1a30610](https://github.com/folke/snacks.nvim/commit/1a30610ab78cce8bb184166de2ef35ee2ca1987a))
* **explorer:** no need to get git status with `-uall`. Closes [#983](https://github.com/folke/snacks.nvim/issues/983) ([bc087d3](https://github.com/folke/snacks.nvim/commit/bc087d36d6126ccf25f8bb3ead405ec32547d85d))
* **picker.list:** only re-render when visible items changed ([c72e62e](https://github.com/folke/snacks.nvim/commit/c72e62ef9012161ec6cd86aa749d780f77d1cc87))
* **picker:** cache treesitter line highlights ([af31c31](https://github.com/folke/snacks.nvim/commit/af31c312872cab2a47e17ed2ee67bf5940a522d4))
* **picker:** cache wether ts lang exists and disable smooth scrolling on big files ([719b36f](https://github.com/folke/snacks.nvim/commit/719b36fa70c35a7015537aa0bfd2956f6128c87d))

## [2.18.0](https://github.com/folke/snacks.nvim/compare/v2.17.0...v2.18.0) (2025-02-03)


### Features

* **dashboard:** play nice with file explorer netrw replacement ([5420a64](https://github.com/folke/snacks.nvim/commit/5420a64b66fd7350f5bb9d5dea2372850ea59969))
* **explorer.git:** added git_status_open. When false, then dont show recursive git status in open directories ([8646ba4](https://github.com/folke/snacks.nvim/commit/8646ba469630b73a34c06243664fb5607c0a43fa))
* **explorer:** added `]g` and `[g` to jump between files mentioned in `git status` ([263dfde](https://github.com/folke/snacks.nvim/commit/263dfde1b598e0fbba5f0031b8976e3c979f553c))
* **explorer:** added git status. Closes [#817](https://github.com/folke/snacks.nvim/issues/817) ([5cae48d](https://github.com/folke/snacks.nvim/commit/5cae48d93c875efa302bdffa995e4b057e2c3731))
* **explorer:** hide git status for open directories by default. it's mostly redundant ([b40c0d4](https://github.com/folke/snacks.nvim/commit/b40c0d4ee4e53aadc5fcf0900e58690c49f9763f))
* **explorer:** keep expanded dir state. Closes [#816](https://github.com/folke/snacks.nvim/issues/816) ([31984e8](https://github.com/folke/snacks.nvim/commit/31984e88a51652bda4997456c53113cbdc811cb4))
* **explorer:** more keymaps and tree rework. See [#837](https://github.com/folke/snacks.nvim/issues/837) ([2ff3893](https://github.com/folke/snacks.nvim/commit/2ff389312a78a8615754c2dee32b96211c9f9c54))
* **explorer:** navigate with h/l to close/open directories. Closes [#833](https://github.com/folke/snacks.nvim/issues/833) ([4b29ddc](https://github.com/folke/snacks.nvim/commit/4b29ddc5d9856ff49a07d77a43634e00b06f4d31))
* **explorer:** new `explorer` module with shortcut to start explorer picker and netrw replacement functionlity ([670c673](https://github.com/folke/snacks.nvim/commit/670c67366f0025fc4ebb78ba35a7586b7477989a))
* **explorer:** recursive copy and copying of selected items with `c` ([2528fcb](https://github.com/folke/snacks.nvim/commit/2528fcb02ceab7b19ee72a94b93c620259881e65))
* **explorer:** update on cwd change ([8dea225](https://github.com/folke/snacks.nvim/commit/8dea2252094ca3dc6d2073ab0015b7bcee396e24))
* **explorer:** update status when saving a file that is currently visible ([78d4116](https://github.com/folke/snacks.nvim/commit/78d4116662d38acb8456ffc6869204b487b472f8))
* **picker.commands:** do not autorun commands that require arguments ([#879](https://github.com/folke/snacks.nvim/issues/879)) ([62d99ed](https://github.com/folke/snacks.nvim/commit/62d99ed2a3711769e34fbc33c7072075824d256a))
* **picker.frecency:** added frecency support for directories ([ce67fa9](https://github.com/folke/snacks.nvim/commit/ce67fa9e31467590c750e203e27d3e6df293f2ad))
* **picker.input:** search syntax highlighting ([4242f90](https://github.com/folke/snacks.nvim/commit/4242f90268c93e7e546c195df26d9f0ee6b10645))
* **picker.lines:** jump to first position of match. Closes [#806](https://github.com/folke/snacks.nvim/issues/806) ([ae897f3](https://github.com/folke/snacks.nvim/commit/ae897f329f06695ee3482e2cd768797d5af3e277))
* **picker.list:** use regular CursorLine when picker window is not focused ([8a570bb](https://github.com/folke/snacks.nvim/commit/8a570bb48ba3536dcfe51f08547896b55fcb0e4d))
* **picker.matcher:** added opts.matcher.history_bonus that does fzf's scheme=history. Closes [#882](https://github.com/folke/snacks.nvim/issues/882). Closes [#872](https://github.com/folke/snacks.nvim/issues/872) ([78c2853](https://github.com/folke/snacks.nvim/commit/78c28535ddd4e3973bcdc9bdf342a37979010918))
* **picker.pickwin:** optional win/buf filter. Closes [#877](https://github.com/folke/snacks.nvim/issues/877) ([5c5b40b](https://github.com/folke/snacks.nvim/commit/5c5b40b5d0ed4166c751a11a839b015f4ad6e26a))
* **picker.projects:** added `&lt;c-t&gt;` to open a new tab page with the projects picker ([ced377a](https://github.com/folke/snacks.nvim/commit/ced377a05783073ab3a8506b5a9b0ffaf8293773))
* **picker.projects:** allow disabling projects from recent files ([c2dedb6](https://github.com/folke/snacks.nvim/commit/c2dedb647f6e170ee4defed647c7f89a51ee9fd0))
* **picker.projects:** default to tcd instead of cd ([3d2a075](https://github.com/folke/snacks.nvim/commit/3d2a07503f0724794a7e262a2f570a13843abedf))
* **picker.projects:** enabled frecency for projects picker ([5a20565](https://github.com/folke/snacks.nvim/commit/5a2056575faa50f788293ee787b803c15f42a9e0))
* **picker.projects:** projects now automatically processes dev folders and added a bunch of actions/keymaps. Closes [#871](https://github.com/folke/snacks.nvim/issues/871) ([6f8f0d3](https://github.com/folke/snacks.nvim/commit/6f8f0d3c727fe035dffc0bc4c1843e2a06eee1f2))
* **picker.undo:** better undo tree visualization. Closes [#863](https://github.com/folke/snacks.nvim/issues/863) ([44b8e38](https://github.com/folke/snacks.nvim/commit/44b8e3820493ca774cd220e3daf85c16954e74c7))
* **picker.undo:** make diff opts for undo configurable ([d61fb45](https://github.com/folke/snacks.nvim/commit/d61fb453c6c23976759e16a33fd8d6cb79cc59bc))
* **picker:** added support for double cliking and confirm ([8b26bae](https://github.com/folke/snacks.nvim/commit/8b26bae6bb01db22dbd3c6f868736487265025c0))
* **picker:** automatically download sqlite3.dll on Windows when using frecency / history for the first time. ([65907f7](https://github.com/folke/snacks.nvim/commit/65907f75ba52c09afc16e3d8d3c7ac67a3916237))
* **picker:** beter API to interact with active pickers. Closes [#851](https://github.com/folke/snacks.nvim/issues/851) ([6a83373](https://github.com/folke/snacks.nvim/commit/6a8337396ad843b27bdfe0a03ac2ce26ccf13092))
* **picker:** better health checks. Fixes [#855](https://github.com/folke/snacks.nvim/issues/855) ([d245925](https://github.com/folke/snacks.nvim/commit/d2459258f1a56109a2ad506f4a4dd6c69f2bb9f2))
* **picker:** history per source. Closes [#843](https://github.com/folke/snacks.nvim/issues/843) ([35295e0](https://github.com/folke/snacks.nvim/commit/35295e0eb2ee261e6173545190bc6c181fd08067))


### Bug Fixes

* **dashboard:** open pull requests with P instead of p in the github exmaple ([b2815d7](https://github.com/folke/snacks.nvim/commit/b2815d7f79e82d09cde5c9bb8e6fd13976b4d618))
* **dashboard:** update on VimResized and WinResized ([558b0ee](https://github.com/folke/snacks.nvim/commit/558b0ee04d0c6e1acf842774fbf9e02cce3efb0e))
* **explorer:** after search, cursor always jumped to top. Closes [#827](https://github.com/folke/snacks.nvim/issues/827) ([d17449e](https://github.com/folke/snacks.nvim/commit/d17449ee90b78843a22ee12ae29c3c110b28eac7))
* **explorer:** always use `--follow` to make sure we see dir symlinks as dirs. Fixes [#814](https://github.com/folke/snacks.nvim/issues/814) ([151fd3d](https://github.com/folke/snacks.nvim/commit/151fd3d62d73e0ec122bb243003c3bd59d53f8ef))
* **explorer:** cwd is now changed automatically, so no need to update state. ([5549d4e](https://github.com/folke/snacks.nvim/commit/5549d4e848b865ad4cc5bbb9bdd9487d631c795b))
* **explorer:** don't disable netrw fully. Just the autocmd that loads a directory ([836eb9a](https://github.com/folke/snacks.nvim/commit/836eb9a4e9ca0d7973f733203871d70691447c2b))
* **explorer:** don't try to show when closed. Fixes [#836](https://github.com/folke/snacks.nvim/issues/836) ([6921cd0](https://github.com/folke/snacks.nvim/commit/6921cd06ac7b530d786b2282afdfce67762008f1))
* **explorer:** fix git status sorting ([551d053](https://github.com/folke/snacks.nvim/commit/551d053c7ccc635249c262a5ea38b5d7aa814b3a))
* **explorer:** fixed hierarchical sorting. Closes [#828](https://github.com/folke/snacks.nvim/issues/828) ([fa32e20](https://github.com/folke/snacks.nvim/commit/fa32e20e9910f8071979f16788832027d1e25850))
* **explorer:** keep global git status cache ([a54a21a](https://github.com/folke/snacks.nvim/commit/a54a21adc0e67b97fb787adcbaaf4578c6f44476))
* **explorer:** remove sleep :) ([efbc4a1](https://github.com/folke/snacks.nvim/commit/efbc4a12af6aae39dadeab0badb84d04a94d5f85))
* **git:** use os.getenv to get env var. Fixes [#5504](https://github.com/folke/snacks.nvim/issues/5504) ([16d700e](https://github.com/folke/snacks.nvim/commit/16d700eb65fc320a5ab8e131d8f5d185b241887b))
* **layout:** adjust zindex when needed when another layout is already open. Closes [#826](https://github.com/folke/snacks.nvim/issues/826) ([ab8af1b](https://github.com/folke/snacks.nvim/commit/ab8af1bb32a4d9f82156122056d07a0850c2a828))
* **layout:** destroy in schedule. Fixes [#861](https://github.com/folke/snacks.nvim/issues/861) ([c955a8d](https://github.com/folke/snacks.nvim/commit/c955a8d1ef543fd56907d5291e92e62fd944db9b))
* **picker.actions:** fix split/vsplit/tab. Closes [#818](https://github.com/folke/snacks.nvim/issues/818) ([ff02241](https://github.com/folke/snacks.nvim/commit/ff022416dd6e6dade2ee822469d0087fcf3e0509))
* **picker.actions:** pass edit commands to jump. Closes [#859](https://github.com/folke/snacks.nvim/issues/859) ([df0e3e3](https://github.com/folke/snacks.nvim/commit/df0e3e3d861fd7b8ab85b3e8dbca97817b3d6604))
* **picker.actions:** reworked split/vsplit/drop/tab cmds to better do what's intended. Closes [#854](https://github.com/folke/snacks.nvim/issues/854) ([2946875](https://github.com/folke/snacks.nvim/commit/2946875af09f5f439ce64b78da8da6cf28523b8c))
* **picker.actions:** tab -&gt; tabnew. Closes [#842](https://github.com/folke/snacks.nvim/issues/842) ([d962d5f](https://github.com/folke/snacks.nvim/commit/d962d5f3359dc91da7aa54388515fd0b03a2fe8b))
* **picker.explorer:** do LSP stuff on move ([894ff74](https://github.com/folke/snacks.nvim/commit/894ff749300342593007e6366894b681b3148f19))
* **picker.explorer:** use cached git status ([1ce435c](https://github.com/folke/snacks.nvim/commit/1ce435c6eb161feae63c8ddfe3e1aaf98b2aa41d))
* **picker.format:** extra slash in path ([dad3e00](https://github.com/folke/snacks.nvim/commit/dad3e00e83ec8a8af92e778e29f2fe200ad0d969))
* **picker.format:** use item.file for filename_only ([e784a9e](https://github.com/folke/snacks.nvim/commit/e784a9e6723371f8f453a92edb03d68428da74cc))
* **picker.git_log:** add extra space between the date and the message ([#885](https://github.com/folke/snacks.nvim/issues/885)) ([d897ead](https://github.com/folke/snacks.nvim/commit/d897ead2b78a73a186a3cb1b735a10f2606ddb36))
* **picker.keymaps:** added normalized lhs to search text ([fbd39a4](https://github.com/folke/snacks.nvim/commit/fbd39a48df085a7df979a06b1003faf86625c157))
* **picker.lazy:** don't use live searches. Fixes [#809](https://github.com/folke/snacks.nvim/issues/809) ([1a5fd93](https://github.com/folke/snacks.nvim/commit/1a5fd93b89904b8f8029e6ee74e6d6ada87f28c5))
* **picker.lines:** col is first non-whitespace. Closes [#806](https://github.com/folke/snacks.nvim/issues/806) ([ec8eb60](https://github.com/folke/snacks.nvim/commit/ec8eb6051530261e7d0e5566721e5c396c1ed6cd))
* **picker.list:** better virtual scrolling that works from any window ([4a50291](https://github.com/folke/snacks.nvim/commit/4a502914486346940389a99690578adca9a820bb))
* **picker.matcher:** fix cwd_bonus check ([00af290](https://github.com/folke/snacks.nvim/commit/00af2909064433ee84280dd64233a34b0f8d6027))
* **picker.matcher:** regex offset by one. Fixes [#878](https://github.com/folke/snacks.nvim/issues/878) ([9a82f0a](https://github.com/folke/snacks.nvim/commit/9a82f0a564df3e4f017e9b66da6baa41196962b7))
* **picker.undo:** add newlines ([72826a7](https://github.com/folke/snacks.nvim/commit/72826a72de93f49b2446c691e3bef04df1a44dde))
* **picker.undo:** cleanup tmp buffer ([8368176](https://github.com/folke/snacks.nvim/commit/83681762435a425ab1edb10fe3244b3e8b1280c2))
* **picker.undo:** copy over buffer lines instead of just the file ([c900e2c](https://github.com/folke/snacks.nvim/commit/c900e2cb3ab83c299c95756fc34e4ae52f4e72e9))
* **picker.undo:** disable swap for tmp undo buffer ([033db25](https://github.com/folke/snacks.nvim/commit/033db250cd688872724a84deb623b599662d79c5))
* **picker:** better main window management. Closes [#842](https://github.com/folke/snacks.nvim/issues/842) ([f0f053a](https://github.com/folke/snacks.nvim/commit/f0f053a1d9b16edaa27f05e20ad6fd862db8c6f7))
* **picker:** improve resume. Closes [#853](https://github.com/folke/snacks.nvim/issues/853) ([0f5b30b](https://github.com/folke/snacks.nvim/commit/0f5b30b41196d831cda84e4b792df2ce765fd856))
* **picker:** make pick_win work with split layouts. Closes [#834](https://github.com/folke/snacks.nvim/issues/834) ([6dbc267](https://github.com/folke/snacks.nvim/commit/6dbc26757cb043c8153a4251a1f75bff4dcadf68))
* **picker:** multi layouts that need async task work again. ([cd44efb](https://github.com/folke/snacks.nvim/commit/cd44efb60ce70382de02d069e269bb40e5e7fa22))
* **picker:** no auto-close when entering a floating window ([08e6c12](https://github.com/folke/snacks.nvim/commit/08e6c12358d57dfb497f8ce7de7eb09134868dc7))
* **picker:** no need to track jumping ([b37ea74](https://github.com/folke/snacks.nvim/commit/b37ea748b6ff56cd479600b1c39d19a308ee7eae))
* **picker:** propagate WinEnter when going to the real window after entering the layout split window ([8555789](https://github.com/folke/snacks.nvim/commit/8555789d86f7f6127fdf023723775207972e0c44))
* **picker:** show regex matches in list when needed. Fixes [#878](https://github.com/folke/snacks.nvim/issues/878) ([1d99bac](https://github.com/folke/snacks.nvim/commit/1d99bac9bcf75a11adc6cd78cde4477a95f014bd))
* **picker:** show_empty for files / grep. Closes [#808](https://github.com/folke/snacks.nvim/issues/808) ([a13ff6f](https://github.com/folke/snacks.nvim/commit/a13ff6fe0f68c3242d6be5e352d762b6037a9695))
* **util:** better default icons when no icon plugin is installed ([0e4ddfd](https://github.com/folke/snacks.nvim/commit/0e4ddfd3ee1d81def4028e52e44e45ac3ce98cfc))
* **util:** better keymap normalization ([e1566a4](https://github.com/folke/snacks.nvim/commit/e1566a483df1badc97729f66b1faf358d2bd3362))
* **util:** normkey. Closes [#763](https://github.com/folke/snacks.nvim/issues/763) ([6972960](https://github.com/folke/snacks.nvim/commit/69729608e101923810a13942f0b3bef98f253592))
* **win:** close help when leaving the win buffer ([4aba559](https://github.com/folke/snacks.nvim/commit/4aba559c6e321f90524a2e8164b8fd1f9329552e))


### Performance Improvements

* **explorer:** don't wait till git status finished. Update tree when needed. See [#869](https://github.com/folke/snacks.nvim/issues/869) ([287db30](https://github.com/folke/snacks.nvim/commit/287db30ed21dc2a8be80fabcffcec9b1b878e04e))
* **explorer:** use cache when possible for opening/closing directories. Closes [#869](https://github.com/folke/snacks.nvim/issues/869) ([cef4fc9](https://github.com/folke/snacks.nvim/commit/cef4fc91813ba6e6932db88a1c9e82a30ea51349))
* **git:** also check top-level path to see if it's a git root. Closes [#807](https://github.com/folke/snacks.nvim/issues/807) ([b9e7c51](https://github.com/folke/snacks.nvim/commit/b9e7c51e8f7eea876275e52f1083b58f9d2df92f))
* **picker.files:** no need to check every time for cmd availability ([8f87c2c](https://github.com/folke/snacks.nvim/commit/8f87c2c32bbb75a4fad4f5768d5faa963c4f66d8))
* **picker.undo:** more performance improvements for the undo picker ([3d4b8ee](https://github.com/folke/snacks.nvim/commit/3d4b8eeea9380eb7488217af74f9448eaa7b376e))
* **picker.undo:** use a tmp buffer to get diffs. Way faster than before. Closes [#863](https://github.com/folke/snacks.nvim/issues/863) ([d4a5449](https://github.com/folke/snacks.nvim/commit/d4a54498131a5d9027bccdf2cd0edd2d22594ce7))

## [2.17.0](https://github.com/folke/snacks.nvim/compare/v2.16.0...v2.17.0) (2025-01-30)


### Features

* **picker.actions:** allow selecting the visual selection with `&lt;Tab&gt;` ([96c76c6](https://github.com/folke/snacks.nvim/commit/96c76c6d9d401c2205d73639389b32470c550e6a))
* **picker.explorer:** focus dir on confirm from search ([605f745](https://github.com/folke/snacks.nvim/commit/605f7451984f0011635423571ad83ab74f342ed8))


### Bug Fixes

* **git:** basic support for git work trees ([d76d9aa](https://github.com/folke/snacks.nvim/commit/d76d9aaaf2399c6cf15c5b37a9183680b106a4af))
* **picker.preview:** properly refresh the preview after layout changes. Fixes [#802](https://github.com/folke/snacks.nvim/issues/802) ([47993f9](https://github.com/folke/snacks.nvim/commit/47993f9a809ce13e98c3132d463d5a3002289fd6))
* **picker:** add proper close ([15a9411](https://github.com/folke/snacks.nvim/commit/15a94117e17d78c8c2e579d20988d4cb9e85d098))
* **picker:** make jumping work again... ([f40f338](https://github.com/folke/snacks.nvim/commit/f40f338d669bf2d54b224e4a973c52c8157fe505))
* **picker:** show help for input / list window with `?`. ([87dab7e](https://github.com/folke/snacks.nvim/commit/87dab7eca7949b85c0ee688f86c08b8c437f9433))
* **win:** properly handle closing the last window. Fixes [#793](https://github.com/folke/snacks.nvim/issues/793) ([18de5bb](https://github.com/folke/snacks.nvim/commit/18de5bb23898fa2055afee5035c97a2abe4aae6b))

## [2.16.0](https://github.com/folke/snacks.nvim/compare/v2.15.0...v2.16.0) (2025-01-30)


### Features

* **layout:** added support for split layouts (root box can be a split) ([6da592e](https://github.com/folke/snacks.nvim/commit/6da592e130295388ee64fe282eb0dafa0b99fa2f))
* **layout:** make fullscreen work for split layouts ([115f8c6](https://github.com/folke/snacks.nvim/commit/115f8c6ae9c9a57b36677b728a6f6cc9207c6489))
* **picker.actions:** added separate hl group for pick win current ([9b80e44](https://github.com/folke/snacks.nvim/commit/9b80e444f548aea26214a95ad9e1affc4ef5d91c))
* **picker.actions:** separate edit_split etc in separate split and edit actions. Fixes [#791](https://github.com/folke/snacks.nvim/issues/791) ([3564f4f](https://github.com/folke/snacks.nvim/commit/3564f4fede6feefdfe1dc200295eb3b162996d6d))
* **picker.config:** added `opts.config` which can be a function that can change the resolved options ([b37f368](https://github.com/folke/snacks.nvim/commit/b37f368a81d0a6ce8b7c9f683f9c0c736af6de36))
* **picker.explorer:** added `explorer_move` action mapped to `m` ([08b9083](https://github.com/folke/snacks.nvim/commit/08b9083f4759c87c93f6afb4af0a1f3d2b8ad1fa))
* **picker.explorer:** live search ([db52796](https://github.com/folke/snacks.nvim/commit/db52796e79c63dfa0d5d689d5d13b120f6184642))
* **picker.files:** allow forcing the files finder to use a certain cmd ([3e1dc30](https://github.com/folke/snacks.nvim/commit/3e1dc300cc98815ad74ae11c98f7ebebde966c39))
* **picker.format:** better path formatting for directories ([08f3c32](https://github.com/folke/snacks.nvim/commit/08f3c32c7d64a81ea35d1cb0d22fc140d25c9088))
* **picker.format:** directory formatting ([847509e](https://github.com/folke/snacks.nvim/commit/847509e12c0cd95355cb05c97e1bc8bedde29957))
* **picker.jump:** added `opts.jump.close`, which default to true, but is false for explorer ([a9591ed](https://github.com/folke/snacks.nvim/commit/a9591ed43f4de3b611028eadce7d36c4b3dedca8))
* **picker.list:** added support for setting a cursor/topline target for the next render. Target clears when reached, or when finders finishes. ([da08379](https://github.com/folke/snacks.nvim/commit/da083792053e41c57e8ca5e9fe9e5b175b1e378d))
* **picker:** `opts.focus = "input"|"list"|false` to configure what to focus (if anything) when showing the picker ([5a8d798](https://github.com/folke/snacks.nvim/commit/5a8d79847b1959f9c9515b51a062f6acbe22f1a4))
* **picker:** `picker:iter()` now also returns `idx` ([118d908](https://github.com/folke/snacks.nvim/commit/118d90899d7b2bb0a28a799dbf2a21ed39516e66))
* **picker:** added `edit_win` action bound to `ctrl+enter` to pick a window and edit ([2ba5be8](https://github.com/folke/snacks.nvim/commit/2ba5be84910d14454292423f08ad83ea213de2ba))
* **picker:** added `git_stash` picker. Closes [#762](https://github.com/folke/snacks.nvim/issues/762) ([bb3db11](https://github.com/folke/snacks.nvim/commit/bb3db117a45da1dabe76f08a75144b028314e6b6))
* **picker:** added `notifications` picker. Closes [#738](https://github.com/folke/snacks.nvim/issues/738) ([32cffd2](https://github.com/folke/snacks.nvim/commit/32cffd2e603ccace129b62c777933a42203c5c77))
* **picker:** added support for split layouts to picker (sidebar and ivy_split) ([5496c22](https://github.com/folke/snacks.nvim/commit/5496c22b6e20a26d2252543029faead946cc2ce9))
* **picker:** added support to keep the picker open when focusing another window (auto_close = false) ([ad8f166](https://github.com/folke/snacks.nvim/commit/ad8f16632c63a3082ea0e80a39cdbd774624532a))
* **picker:** new file explorer `Snacks.picker.explorer()` ([00613bd](https://github.com/folke/snacks.nvim/commit/00613bd4163c89e01c1d534d283cfe531773fdc8))
* **picker:** opening a picker with the same source as an active picker, will close it instead (toggle) ([b6cf033](https://github.com/folke/snacks.nvim/commit/b6cf033051aa2f859d9d217bc60e89806fcf5377))
* **picker:** reworked toggles (flags). they're now configurable. Closes [#770](https://github.com/folke/snacks.nvim/issues/770) ([e16a6a4](https://github.com/folke/snacks.nvim/commit/e16a6a441322c944b41e9ae5b30ba816145218cd))
* **rename:** optional `file`, `on_rename` for `Snacks.rename.rename_file()` ([9d8c277](https://github.com/folke/snacks.nvim/commit/9d8c277bebb9483b1d46c7eeeff348966076347f))


### Bug Fixes

* **bigfile:** check if buf still exists when applying syntax. Fixes [#737](https://github.com/folke/snacks.nvim/issues/737) ([08852ac](https://github.com/folke/snacks.nvim/commit/08852ac7f811f51d47a590d62df805d0e84e611a))
* **bufdelete:** ctrl-c throw error in `fn.confirm` ([#750](https://github.com/folke/snacks.nvim/issues/750)) ([3a3e795](https://github.com/folke/snacks.nvim/commit/3a3e79535bb085815d3add9f678d30b98b5f900f))
* **bufdelete:** invalid lua ([b1f4f99](https://github.com/folke/snacks.nvim/commit/b1f4f99a51ef1ca11a0c802b847501b71f09161b))
* **dashboard:** better handling of closed dashboard win ([6cb7fdf](https://github.com/folke/snacks.nvim/commit/6cb7fdfb036239b9c1b6d147633e494489a45191))
* **dashboard:** don't override user configuration ([#774](https://github.com/folke/snacks.nvim/issues/774)) ([5ff2ad3](https://github.com/folke/snacks.nvim/commit/5ff2ad320b0cd1e17d48862c74af0df205894f37))
* **dashboard:** fix dasdhboard when opening in a new win. Closes [#767](https://github.com/folke/snacks.nvim/issues/767) ([d44b978](https://github.com/folke/snacks.nvim/commit/d44b978d7dbe7df8509a172cef0913b5a9ac77e3))
* **dashboard:** prevent starting picker twice when no session manager. Fixes [#783](https://github.com/folke/snacks.nvim/issues/783) ([2f396b3](https://github.com/folke/snacks.nvim/commit/2f396b341dc1a072643eb402bfaa8a73f6be19a1))
* **filter:** insert path from `filter.paths` into `self.paths` ([#761](https://github.com/folke/snacks.nvim/issues/761)) ([ac20c6f](https://github.com/folke/snacks.nvim/commit/ac20c6ff5d0ac8747e164d592e8ae7e8f2581b2e))
* **layout:** better handling of resizing of split layouts ([c8ce9e2](https://github.com/folke/snacks.nvim/commit/c8ce9e2b33623d21901e02213319270936e4545f))
* **layout:** better update check for split layouts ([b50d697](https://github.com/folke/snacks.nvim/commit/b50d697ce45dbee5efe25371428b7f23b037d0ed))
* **layout:** make sure split layouts are still visible when a float layout with backdrop opens ([696d198](https://github.com/folke/snacks.nvim/commit/696d1981b18ad1f0cc0e480aafed78a064730417))
* **matcher.score:** correct prev_class for transition bonusses when in a gap. Fixes [#787](https://github.com/folke/snacks.nvim/issues/787) ([b45d0e0](https://github.com/folke/snacks.nvim/commit/b45d0e03579c80ac901261e0e2705a1be3dfcb20))
* **picker.actions:** detect and report circular action references ([0ffc003](https://github.com/folke/snacks.nvim/commit/0ffc00367a957c1602df745c2038600d48d96305))
* **picker.actions:** proper cr check ([6c9f866](https://github.com/folke/snacks.nvim/commit/6c9f866b3123cbc8cbef91f55593d30d98d4f26a))
* **picker.actions:** stop pick_win when no target and only unhide when picker wasn't stopped ([4a1d189](https://github.com/folke/snacks.nvim/commit/4a1d189f9f7afac4180f7a459597bb094d11435b))
* **picker.actions:** when only 1 win, `pick_win` will select that automatically. Show warning when no windows. See [#623](https://github.com/folke/snacks.nvim/issues/623) ([ba5a70b](https://github.com/folke/snacks.nvim/commit/ba5a70b84d12aa9e497cfea86d5358aa5cf0aad3))
* **picker.config:** fix wrong `opts.cwd = true` config. Closes [#757](https://github.com/folke/snacks.nvim/issues/757) ([ea44a2d](https://github.com/folke/snacks.nvim/commit/ea44a2d4c21aa10fb57fe08b974999f7b8d117d2))
* **picker.config:** normalize `opts.cwd` ([69c013e](https://github.com/folke/snacks.nvim/commit/69c013e1b27e2f70def48576aaffcc1081fa0e47))
* **picker.explorer:** fix cwd for items ([71070b7](https://github.com/folke/snacks.nvim/commit/71070b78f0482a42448da2cee64ed0d84c507314))
* **picker.explorer:** stop file follow when picker was closed ([89fcb3b](https://github.com/folke/snacks.nvim/commit/89fcb3bb2025cb1c986e9af3478715f6e0bdf425))
* **picker.explorer:** when searching, go to first non internal node in the list ([276497b](https://github.com/folke/snacks.nvim/commit/276497b3969cdefd18aa731c5e3d5c1bb8289cca))
* **picker.filter:** proper cwd check. See [#757](https://github.com/folke/snacks.nvim/issues/757) ([e4ae9e3](https://github.com/folke/snacks.nvim/commit/e4ae9e32295d688a1e0d3f59ab1ba4cc78d1ba89))
* **picker.git:** better stash pattern. Closes [#775](https://github.com/folke/snacks.nvim/issues/775) ([e960010](https://github.com/folke/snacks.nvim/commit/e960010496f860d1077f1e54d193e127ad7e26ad))
* **picker.git:** default to git root for `git_files`. Closes [#751](https://github.com/folke/snacks.nvim/issues/751) ([3cdebee](https://github.com/folke/snacks.nvim/commit/3cdebee880742970df1a1f685be4802b90642c7d))
* **picker.git:** ignore autostash ([2b15357](https://github.com/folke/snacks.nvim/commit/2b15357c25db315567f08e7ec8d5c85c94d0753f))
* **picker.git:** show diff for staged files. Fixes [#747](https://github.com/folke/snacks.nvim/issues/747) ([e87f0ff](https://github.com/folke/snacks.nvim/commit/e87f0ffcd100a3a6686549e25f480c1311f08d8f))
* **picker.layout:** fix list cursorline when layout updates ([3f43026](https://github.com/folke/snacks.nvim/commit/3f43026f579f33b679a924dea699df86e8b965b2))
* **picker.layout:** make split layouts work in layout preview ([215ae72](https://github.com/folke/snacks.nvim/commit/215ae72daaed5d7ee18b72e8b14bfd6a727bc939))
* **picker.lsp:** remove symbol detail from search text. too noisy ([92710df](https://github.com/folke/snacks.nvim/commit/92710dfb0bacc72a82e0172bb06f5eb9ad82964a))
* **picker.multi:** apply source filter settings for multi source pickers. See [#761](https://github.com/folke/snacks.nvim/issues/761) ([4e30ff0](https://github.com/folke/snacks.nvim/commit/4e30ff0f1ed58b0bdc8fd3f5f1a9a440959eb998))
* **picker.preview:** don't enable numbers when minimal=true ([04e2995](https://github.com/folke/snacks.nvim/commit/04e2995bbfc505d0fc91263712d0255f102f404e))
* **picker.preview:** don't error on invalid start positions for regex. Fixes [#784](https://github.com/folke/snacks.nvim/issues/784) ([460b58b](https://github.com/folke/snacks.nvim/commit/460b58bdbd57e32a1bed22b3e176fa53befeafaa))
* **picker.preview:** only show binary message when binary and no ft. Closes [#729](https://github.com/folke/snacks.nvim/issues/729) ([ea838e2](https://github.com/folke/snacks.nvim/commit/ea838e28383d74d60cd6d714cac9b007a4a4469a))
* **picker.resume:** fix picker is nil ([#772](https://github.com/folke/snacks.nvim/issues/772)) ([1a5a087](https://github.com/folke/snacks.nvim/commit/1a5a0871c822e5de8e69c10bb1d6cb3dfc2f5c86))
* **picker.score:** scoring closer to fzf. See [#787](https://github.com/folke/snacks.nvim/issues/787) ([390f017](https://github.com/folke/snacks.nvim/commit/390f017c3b227377c09ae6572f88b7c42304b811))
* **picker.select:** allow configuring `vim.ui.select` with the `select` source. Closes [#776](https://github.com/folke/snacks.nvim/issues/776) ([d70af2d](https://github.com/folke/snacks.nvim/commit/d70af2d253f61164a44a8676a5fc316cad10497f))
* **picker.util:** proper cwd check for paths. Fixes [#754](https://github.com/folke/snacks.nvim/issues/754). See [#757](https://github.com/folke/snacks.nvim/issues/757) ([1069d78](https://github.com/folke/snacks.nvim/commit/1069d783347a7a5213240e2e12e485ab57e15bd8))
* **picker:** better handling of win Enter/Leave mostly for split layouts ([046653a](https://github.com/folke/snacks.nvim/commit/046653a4f166633339a276999738bac43c3c1388))
* **picker:** don't destroy active pickers (only an issue when multiple pickers were open) ([b479f10](https://github.com/folke/snacks.nvim/commit/b479f10b24a8cf5325bc575e1bab2fc51ebfde45))
* **picker:** only do preview scrolling when preview is scrolling and removed default preview horizontal scrolling keymaps ([a998c71](https://github.com/folke/snacks.nvim/commit/a998c714c31ab92a06b9edafef71251b63f0eb5b))
* **picker:** show new notifications on top ([0df7c0b](https://github.com/folke/snacks.nvim/commit/0df7c0bef59be861f3e6682aa1381f6422f4a0af))
* **picker:** split edit_win in `{"pick_win", "jump"}` ([dcd3bc0](https://github.com/folke/snacks.nvim/commit/dcd3bc03295a8521773c04671298bd3fdcb14f7b))
* **picker:** stopinsert again ([2250c57](https://github.com/folke/snacks.nvim/commit/2250c57529b1a8da4d96966db1cd9a46b73d8007))
* **win:** don't destroy opts. Fixes [#726](https://github.com/folke/snacks.nvim/issues/726) ([473be03](https://github.com/folke/snacks.nvim/commit/473be039e59730b0554a7dfda2eb800ecf7a948e))
* **win:** error when enabling padding with `listchars=""` ([#786](https://github.com/folke/snacks.nvim/issues/786)) ([6effbcd](https://github.com/folke/snacks.nvim/commit/6effbcdff110c16f49c2cef5d211db86f6db5820))


### Performance Improvements

* **picker.matcher:** optimize matcher priorities and skip items that can't match for pattern subset ([dfaa18d](https://github.com/folke/snacks.nvim/commit/dfaa18d1c72a78cacfe0a682c853b7963641444c))
* **picker.recent:** correct generator for old files ([5f32414](https://github.com/folke/snacks.nvim/commit/5f32414dd645ab7650dc60379f422b00aaecea4f))
* **picker.score:** no need to track `in_gap` status. redundant since we can depend on `gap` instead ([fdf4b0b](https://github.com/folke/snacks.nvim/commit/fdf4b0bf26743eef23e645235915aa4920827daf))

## [2.15.0](https://github.com/folke/snacks.nvim/compare/v2.14.0...v2.15.0) (2025-01-23)


### Features

* **debug:** truncate inspect to 2000 lines max ([570d219](https://github.com/folke/snacks.nvim/commit/570d2191d598d344ddd5b2a85d8e79d207955cc3))
* **input:** input history. Closes [#591](https://github.com/folke/snacks.nvim/issues/591) ([80db91f](https://github.com/folke/snacks.nvim/commit/80db91f03e3493e9b3aa09d1cd90b063ae0ec31c))
* **input:** persistent history. Closes [#591](https://github.com/folke/snacks.nvim/issues/591) ([0ed68bd](https://github.com/folke/snacks.nvim/commit/0ed68bdf7268bf1baef7a403ecc799f2c016b656))
* **picker.debug:** more info about potential leaks ([8d9677f](https://github.com/folke/snacks.nvim/commit/8d9677fc479710ae1f531fc52b0ac368def55b0b))
* **picker.filter:** Filter arg for filter ([5a4b684](https://github.com/folke/snacks.nvim/commit/5a4b684c0dd3eda10ce86f9710e085431a7656f2))
* **picker.finder:** optional transform function ([5e69fb8](https://github.com/folke/snacks.nvim/commit/5e69fb83d50bb79ff62352418733d11562e488d0))
* **picker.format:** `filename_only` option ([0396bdf](https://github.com/folke/snacks.nvim/commit/0396bdfc3eece8438ed6a978f1dbddf3f675ca36))
* **picker.git:** git_log, git_log_file, git_log_line now do git_checkout as confirm. Closes [#722](https://github.com/folke/snacks.nvim/issues/722) ([e6fb538](https://github.com/folke/snacks.nvim/commit/e6fb5381a9bfcbd0f1693ea9c7e3711045380187))
* **picker.help:** add more color to help tags ([5778234](https://github.com/folke/snacks.nvim/commit/5778234e3917999a0be1a5b8145dd83ab41035b3))
* **picker.keymaps:** add global + buffer toggles ([#705](https://github.com/folke/snacks.nvim/issues/705)) ([b7c08df](https://github.com/folke/snacks.nvim/commit/b7c08df2b8ff23e0293cfe06beaf60aa6fd14efc))
* **picker.keymaps:** improvements to keymaps picker ([2762c37](https://github.com/folke/snacks.nvim/commit/2762c37eb09bc434eba647d4ec079d6064d3c563))
* **picker.matcher:** frecency and cwd bonus can now be enabled on any picker ([7b85dfc](https://github.com/folke/snacks.nvim/commit/7b85dfc6f60538b0419ca1b969553891b64cd9b8))
* **picker.multi:** multi now also merges keymaps ([8b2c78a](https://github.com/folke/snacks.nvim/commit/8b2c78a3bf5a3ca52c8c9e46b9d15c288c59c5c1))
* **picker.preview:** better positioning of preview location ([3864955](https://github.com/folke/snacks.nvim/commit/38649556ee9f831e5d456043a796ae0fb115f8eb))
* **picker.preview:** fallback highlight of results when no `end_pos`. Mostly useful for grep. ([d12e454](https://github.com/folke/snacks.nvim/commit/d12e45433960210a16a37adc116e645e253578c1))
* **picker.smart:** add bufdelete actions from buffers picker ([#679](https://github.com/folke/snacks.nvim/issues/679)) ([67fbab1](https://github.com/folke/snacks.nvim/commit/67fbab1bf7f5c436e28af715097eecb2232eea59))
* **picker.smart:** re-implemented smart as multi-source picker ([450d1d4](https://github.com/folke/snacks.nvim/commit/450d1d4d4c218ac1df63924d29717caa61c98f27))
* **picker.util:** smart path truncate. Defaults to 40. Closes [#708](https://github.com/folke/snacks.nvim/issues/708) ([bab8243](https://github.com/folke/snacks.nvim/commit/bab8243395de8d8748a7295906bee7723b7b8190))
* **picker:** added `lazy` source to search for a plugin spec. Closes [#680](https://github.com/folke/snacks.nvim/issues/680) ([d03bd00](https://github.com/folke/snacks.nvim/commit/d03bd00ced5a03f1cb317b9227a6a1812e35a6c2))
* **picker:** added `opts.rtp` (bool) to find/grep over files in the rtp. See [#680](https://github.com/folke/snacks.nvim/issues/680) ([9d5d3bd](https://github.com/folke/snacks.nvim/commit/9d5d3bdb1747669d74587662cce93021fc99f878))
* **picker:** added new `icons` picker for nerd fonts and emoji. Closes [#703](https://github.com/folke/snacks.nvim/issues/703) ([97898e9](https://github.com/folke/snacks.nvim/commit/97898e910d92e50e886ba044ab255360e4271ffc))
* **picker:** getters and setters for cwd ([2c2ff4c](https://github.com/folke/snacks.nvim/commit/2c2ff4caf85ba1cfee3d946ea6ab9fd595ec3667))
* **picker:** multi source picker. Combine multiple pickers (as opposed to just finders) in one picker ([9434986](https://github.com/folke/snacks.nvim/commit/9434986ff15acfca7e010f159460f9ecfee81363))
* **picker:** persistent history. Closes [#528](https://github.com/folke/snacks.nvim/issues/528) ([ea665eb](https://github.com/folke/snacks.nvim/commit/ea665ebad18a8ccd6444df7476237de4164af64a))
* **picker:** preview window horizontal scrolling ([#686](https://github.com/folke/snacks.nvim/issues/686)) ([bc47e0b](https://github.com/folke/snacks.nvim/commit/bc47e0b1dd0102b58a90aba87f22e0cc0a48985c))
* **picker:** syntax highlighting for command and search history ([efb6d1f](https://github.com/folke/snacks.nvim/commit/efb6d1f8b8057e5f8455452c47ab769358902a18))
* **profiler:** added support for `Snacks.profiler` and dropped support for fzf-lua / telescope. Closes [#695](https://github.com/folke/snacks.nvim/issues/695) ([ada83de](https://github.com/folke/snacks.nvim/commit/ada83de9528d0825928726a6d252fb97271fb73a))


### Bug Fixes

* **picker.actions:** `checktime` after `git_checkout` ([b86d90e](https://github.com/folke/snacks.nvim/commit/b86d90e3e9c68f4d24a0208e873d35b0074c12b0))
* **picker.async:** better handling of abort and schedule/defer util function ([dfcf27e](https://github.com/folke/snacks.nvim/commit/dfcf27e2a90d4b262d2bd0e54c1b576dba296c73))
* **picker.async:** fixed aborting a coroutine from the coroutine itself. See [#665](https://github.com/folke/snacks.nvim/issues/665) ([c1e2c61](https://github.com/folke/snacks.nvim/commit/c1e2c619b229a3f2ccdc000a6fadddc7ca9f482d))
* **picker.files:** include symlinks ([dc9c6fb](https://github.com/folke/snacks.nvim/commit/dc9c6fbd028e0488a9292e030c788b72b16cbeca))
* **picker.frecency:** track visit on BufWinEnter instead of BufReadPost and exclude floating windows ([024a448](https://github.com/folke/snacks.nvim/commit/024a448e52563aadf9e5b234ddfb17168aa5ada7))
* **picker.git_branches:** handle detached HEAD ([#671](https://github.com/folke/snacks.nvim/issues/671)) ([390f687](https://github.com/folke/snacks.nvim/commit/390f6874318addcf48b668f900ef62d316c44602))
* **picker.git:** `--follow` only works for `git_log_file`. Closes [#666](https://github.com/folke/snacks.nvim/issues/666) ([23a8668](https://github.com/folke/snacks.nvim/commit/23a8668ef0b0c9d910c7bbcd57651d8889b0fa65))
* **picker.git:** parse all detached states. See [#671](https://github.com/folke/snacks.nvim/issues/671) ([2cac667](https://github.com/folke/snacks.nvim/commit/2cac6678a95f89a7e23ed668c9634eff9e60dbe5))
* **picker.grep:** off-by-one for grep results col ([e3455ef](https://github.com/folke/snacks.nvim/commit/e3455ef4dc96fac3b53f76e12c487007a5fca9e7))
* **picker.icons:** bump build for nerd fonts ([ba108e2](https://github.com/folke/snacks.nvim/commit/ba108e2aa86909168905e522342859ec9ed4e220))
* **picker.icons:** fix typo in Nerd Fonts and display the full category name ([#716](https://github.com/folke/snacks.nvim/issues/716)) ([a4b0a85](https://github.com/folke/snacks.nvim/commit/a4b0a85e3bc68fe1aeca1ee4161053dabaeb856c))
* **picker.icons:** opts.icons -&gt; opts.icon_sources. Fixes [#715](https://github.com/folke/snacks.nvim/issues/715) ([9e7bfc0](https://github.com/folke/snacks.nvim/commit/9e7bfc05d5e4a0f079f695cdd6869c219c762224))
* **picker.input:** better handling of `stopinsert` with prompt buffers. Closes [#723](https://github.com/folke/snacks.nvim/issues/723) ([c2916cb](https://github.com/folke/snacks.nvim/commit/c2916cb526d42fb6726cf9f7252ceb44516fc2f6))
* **picker.input:** correct cursor position in input when cycling / focus. Fixes [#688](https://github.com/folke/snacks.nvim/issues/688) ([93cca7a](https://github.com/folke/snacks.nvim/commit/93cca7a4b3923c291726305b301a51316b275bf2))
* **picker.lsp:** include_current on Windows. Closes [#678](https://github.com/folke/snacks.nvim/issues/678) ([66d2854](https://github.com/folke/snacks.nvim/commit/66d2854ea0c83339042b6340b29dfdc48982e75a))
* **picker.lsp:** make `lsp_symbols` work for unloaded buffers ([9db49b7](https://github.com/folke/snacks.nvim/commit/9db49b7e6c5ded7edeff8bec6327322fb6125695))
* **picker.lsp:** schedule_wrap cancel functions and resume when no clients ([6cbca8a](https://github.com/folke/snacks.nvim/commit/6cbca8adffd4014e9f67ba327f9c164f0412b685))
* **picker.lsp:** use async from ctx ([b878caa](https://github.com/folke/snacks.nvim/commit/b878caaddc7b91386ec95b3b2f034b275dc7f49a))
* **picker.lsp:** use correct buf/win ([8006caa](https://github.com/folke/snacks.nvim/commit/8006caadb3eedf2553a587497c508c01aadf098b))
* **picker.preview:** clear buftype for file previews ([5429dff](https://github.com/folke/snacks.nvim/commit/5429dff1cd51ceaa10134dbff4faf447823de017))
* **picker.undo:** use new API. Closes [#707](https://github.com/folke/snacks.nvim/issues/707) ([79a6eab](https://github.com/folke/snacks.nvim/commit/79a6eabd318d2b65d5786c4e3c2419eaa91c6240))
* **picker.util:** for `--` args require a space before ([ee6f21b](https://github.com/folke/snacks.nvim/commit/ee6f21bbd636e82691a0386f39f0c8310f8cadd8))
* **picker.util:** more relaxed resolve_loc ([964beb1](https://github.com/folke/snacks.nvim/commit/964beb11489afc2a2a1004bbb1b2b3286da9a8ac))
* **picker.util:** prevent empty shortened paths if it's the cwd. Fixes [#721](https://github.com/folke/snacks.nvim/issues/721) ([14f16ce](https://github.com/folke/snacks.nvim/commit/14f16ceb5d4dc53ddbd9b56992335658105d1d5f))
* **picker:** better handling of buffers with custom URIs. Fixes [#677](https://github.com/folke/snacks.nvim/issues/677) ([cd5eddb](https://github.com/folke/snacks.nvim/commit/cd5eddb1dea0ab69a451702395104cf716678b36))
* **picker:** don't jump to invalid positions. Fixes [#712](https://github.com/folke/snacks.nvim/issues/712) ([51adb67](https://github.com/folke/snacks.nvim/commit/51adb6792e1819c9cf0153606f506403f97647fe))
* **picker:** don't try showing the preview when the picker is closed. Fixes [#714](https://github.com/folke/snacks.nvim/issues/714) ([11c0761](https://github.com/folke/snacks.nvim/commit/11c07610557f0a6c6eb40bca60c60982ff6e3b93))
* **picker:** resume. Closes [#709](https://github.com/folke/snacks.nvim/issues/709) ([9b55a90](https://github.com/folke/snacks.nvim/commit/9b55a907bd0468752c3e5d9cd7e607cab206a6d7))
* **picker:** starting a picker from the picker sometimes didnt start in insert mode. Fixes [#718](https://github.com/folke/snacks.nvim/issues/718) ([08d4f14](https://github.com/folke/snacks.nvim/commit/08d4f14cd85466fd37d63b7123437c7d15bc87f6))
* **picker:** update title on find. Fixes [#717](https://github.com/folke/snacks.nvim/issues/717) ([431a24e](https://github.com/folke/snacks.nvim/commit/431a24e24e2a7066e44272f83410d7b44f497e26))
* **scroll:** handle buffer changes while animating ([3da0b0e](https://github.com/folke/snacks.nvim/commit/3da0b0ec11dff6c88e68c91194688c9ff3513e86))
* **win:** better way of finding a main window when fixbuf is `true` ([84ee7dd](https://github.com/folke/snacks.nvim/commit/84ee7ddf543aa1249ca4e29873200073e28f693f))
* **zen:** parent buf. Fixes [#720](https://github.com/folke/snacks.nvim/issues/720) ([f314676](https://github.com/folke/snacks.nvim/commit/f31467637ac91406efba15981d53cd6da09718e0))


### Performance Improvements

* **picker.frecency:** cache all deadlines on load ([5b3625b](https://github.com/folke/snacks.nvim/commit/5b3625bcea5ed78e7cddbeb038159a0041110c71))
* **picker:** gc optims ([3fa2ea3](https://github.com/folke/snacks.nvim/commit/3fa2ea3115c2e8203ec44025ff4be054c5f1e917))
* **picker:** small optims ([ee76e9b](https://github.com/folke/snacks.nvim/commit/ee76e9ba674e6b67a3d687868f27751745e2baad))
* **picker:** small optims for abort ([317a209](https://github.com/folke/snacks.nvim/commit/317a2093ea0cdd62a34f3a414e625f3313e5e2e8))
* **picker:** use picker ref in progress updater. Fixes [#710](https://github.com/folke/snacks.nvim/issues/710) ([37f3888](https://github.com/folke/snacks.nvim/commit/37f3888dccc922e4044ee1713c25dba51b4290d2))

## [2.14.0](https://github.com/folke/snacks.nvim/compare/v2.13.0...v2.14.0) (2025-01-20)


### Features

* **picker.buffer:** add filetype to bufname for buffers without name ([83baea0](https://github.com/folke/snacks.nvim/commit/83baea06d65d616f1f800501d0d82e4ad117abf2))
* **picker.debug:** debug option to detect garbage collection leaks ([b59f4ff](https://github.com/folke/snacks.nvim/commit/b59f4ff477a18cdc3673a240c2e992a2bccd48fe))
* **picker.matcher:** new `opts.matcher.file_pos` which defaults to `true` to support patterns like `file:line:col` or `file:line`. Closes [#517](https://github.com/folke/snacks.nvim/issues/517). Closes [#496](https://github.com/folke/snacks.nvim/issues/496). Closes [#651](https://github.com/folke/snacks.nvim/issues/651) ([5e00b0a](https://github.com/folke/snacks.nvim/commit/5e00b0ab271149f1bd74d5d5afe106b440f45a9d))
* **picker:** added `args` option for `files` and `grep`. Closes [#621](https://github.com/folke/snacks.nvim/issues/621) ([781b6f6](https://github.com/folke/snacks.nvim/commit/781b6f6ab0cd5f65a685bf8bac284f4a12e43589))
* **picker:** added `undo` picker to navigate the undo tree. Closes [#638](https://github.com/folke/snacks.nvim/issues/638) ([5c45f1c](https://github.com/folke/snacks.nvim/commit/5c45f1c193f2ed2fa639146df373f341d7410e8b))
* **picker:** added support for item.resolve that gets called if needed during list rendering / preview ([b0d3266](https://github.com/folke/snacks.nvim/commit/b0d32669856b8ad9c75fa7c6c4b643566001c8bc))
* **terminal:** allow overriding default shell. Closes [#450](https://github.com/folke/snacks.nvim/issues/450) ([3146fd1](https://github.com/folke/snacks.nvim/commit/3146fd139b89760526f32fd9d3ac4c91af010f0c))
* **terminal:** close terminals on `ExitPre`. Fixes [#419](https://github.com/folke/snacks.nvim/issues/419) ([2abf208](https://github.com/folke/snacks.nvim/commit/2abf208f2c43a387ca6c55c33b5ebbc7869c189c))


### Bug Fixes

* **dashboard:** added optional filter for recent files ([32cd343](https://github.com/folke/snacks.nvim/commit/32cd34383c8ac5d0e43408aba559308546555962))
* **debug.run:** schedule only nvim_buf_set_extmark in on_print ([#425](https://github.com/folke/snacks.nvim/issues/425)) ([81572b5](https://github.com/folke/snacks.nvim/commit/81572b5f97c3cb2f2e254972762f4b816e790fde))
* **indent:** use correct hl based on indent. Fixes [#422](https://github.com/folke/snacks.nvim/issues/422) ([627af73](https://github.com/folke/snacks.nvim/commit/627af7342cf5bea06793606c33992d2cc882655b))
* **input:** put the cursor right after the default prompt ([#549](https://github.com/folke/snacks.nvim/issues/549)) ([f904481](https://github.com/folke/snacks.nvim/commit/f904481439706e678e93225372b30f97281cfc2c))
* **notifier:** added `SnacksNotifierMinimal`. Closes [#410](https://github.com/folke/snacks.nvim/issues/410) ([daa575e](https://github.com/folke/snacks.nvim/commit/daa575e3cd42f003e171dbb8a3e992e670d5032c))
* **notifier:** win:close instead of win:hide ([f29f7a4](https://github.com/folke/snacks.nvim/commit/f29f7a433a2d9ea95f43c163d57df2f647700115))
* **picker.buffers:** add buf number to text ([70106a7](https://github.com/folke/snacks.nvim/commit/70106a79306525a281a3156bae1499f70c183d1d))
* **picker.buffer:** unselect on delete. Fixes [#653](https://github.com/folke/snacks.nvim/issues/653) ([0ac5605](https://github.com/folke/snacks.nvim/commit/0ac5605bfbeb31cee4bb91a6ca7a2bfe8c4d468f))
* **picker.grep:** correctly insert args from pattern. See [#601](https://github.com/folke/snacks.nvim/issues/601) ([8601a8c](https://github.com/folke/snacks.nvim/commit/8601a8ced398145d95f118737b29f3bd5f7eb700))
* **picker.grep:** debug ([f0d51ce](https://github.com/folke/snacks.nvim/commit/f0d51ce03835815aba0a6d748b54c3277ff38b70))
* **picker.lsp.symbols:** only include filename for search with workspace symbols ([eb0e5b7](https://github.com/folke/snacks.nvim/commit/eb0e5b7efe603bea7a0823ffaed13c52b395d04b))
* **picker.lsp:** backward compat with Neovim 0.95 ([3df2408](https://github.com/folke/snacks.nvim/commit/3df2408713efdbc72f9a8fcedc8586aed50ab023))
* **picker.lsp:** lazy resolve item lsp locations. Fixes [#650](https://github.com/folke/snacks.nvim/issues/650) ([d0a0046](https://github.com/folke/snacks.nvim/commit/d0a0046e37d274d8acdfcde653f3cadb12be6ba1))
* **picker.preview:** disable relativenumber by default. Closes [#664](https://github.com/folke/snacks.nvim/issues/664) ([384b9a7](https://github.com/folke/snacks.nvim/commit/384b9a7a36b5e30959fd89d3d857855105f65611))
* **picker.preview:** off-by-one for cmd output ([da5556a](https://github.com/folke/snacks.nvim/commit/da5556aa6bceb3428700607ab3005e5b44cb8b2e))
* **picker.preview:** reset before notify ([e50f2e3](https://github.com/folke/snacks.nvim/commit/e50f2e39094d4511506329044713ac69541f4135))
* **picker.undo:** disable number and signcolumn in preview ([40cea79](https://github.com/folke/snacks.nvim/commit/40cea79697acc97c3e4f814ca99a2d261fd6a4ee))
* **picker.util:** item.resolve for nil item ([2ff21b4](https://github.com/folke/snacks.nvim/commit/2ff21b4394d1f34887cb3425e32f18a793b749c7))
* **picker.util:** relax pattern for args ([6b7705c](https://github.com/folke/snacks.nvim/commit/6b7705c7edc9b93f16179d1343f9b2ae062340f9))
* **scope:** parse treesitter injections. Closes [#430](https://github.com/folke/snacks.nvim/issues/430) ([985ada3](https://github.com/folke/snacks.nvim/commit/985ada3c14346cc6df6a6013564a6541c66f6ce9))
* **statusline:** fix status line cache key ([#656](https://github.com/folke/snacks.nvim/issues/656)) ([af55934](https://github.com/folke/snacks.nvim/commit/af559349e591afaaaf75a8b3ecf5ee6f6711dde0))
* **win:** always close created scratch buffers when win closes ([abd7e61](https://github.com/folke/snacks.nvim/commit/abd7e61b7395af10a7862cec5bc746253a3b7917))
* **zen:** properly handle close ([920a9d2](https://github.com/folke/snacks.nvim/commit/920a9d28f1b1bf5ca06755236f9bbb8853adfea8))
* **zen:** sync cursor with parent window ([#547](https://github.com/folke/snacks.nvim/issues/547)) ([ba45c28](https://github.com/folke/snacks.nvim/commit/ba45c280dd9a35a6441a89d830b72f7cc8849b74)), closes [#539](https://github.com/folke/snacks.nvim/issues/539)


### Performance Improvements

* **picker:** fixed some issues with closed pickers not always being garbage collected ([eebf44a](https://github.com/folke/snacks.nvim/commit/eebf44a34e9e004f988437116140712834efd745))

## [2.13.0](https://github.com/folke/snacks.nvim/compare/v2.12.0...v2.13.0) (2025-01-19)


### Features

* **picker.actions:** added support for action options. Fixes [#598](https://github.com/folke/snacks.nvim/issues/598) ([8035398](https://github.com/folke/snacks.nvim/commit/8035398e523588df7eac928fd23e6692522f6e1e))
* **picker.buffers:** del buffer with ctrl+x ([2479ff7](https://github.com/folke/snacks.nvim/commit/2479ff7cf41392130bd660fb787e3b1730863657))
* **picker.buffers:** delete buffers with dd ([2ab18a0](https://github.com/folke/snacks.nvim/commit/2ab18a0b9f425ccbc697adc53a01b26ea38abe0d))
* **picker.commands:** added builtin commands. Fixes [#634](https://github.com/folke/snacks.nvim/issues/634) ([ee988fa](https://github.com/folke/snacks.nvim/commit/ee988fa4b018ae617a16e2a4078b4586f08364f2))
* **picker.frecency:** cleanup old entries from sqlite3 database ([320a4a6](https://github.com/folke/snacks.nvim/commit/320a4a62a159f9d3177251e21d81cb96156291b9))
* **picker.git:** added `git_diff` picker for diff hunks ([#519](https://github.com/folke/snacks.nvim/issues/519)) ([cc69043](https://github.com/folke/snacks.nvim/commit/cc690436892d6ab0b8d5ee51ad60ff91c3a5d640))
* **picker.git:** git diff/show can now use native or neovim for preview. defaults to neovim. Closes [#500](https://github.com/folke/snacks.nvim/issues/500). Closes [#494](https://github.com/folke/snacks.nvim/issues/494). Closes [#491](https://github.com/folke/snacks.nvim/issues/491). Closes [#478](https://github.com/folke/snacks.nvim/issues/478) ([e36e6af](https://github.com/folke/snacks.nvim/commit/e36e6af96cb2ac0574199ab9d229735fb6d9f016))
* **picker.git:** stage/unstage files in git status with `&lt;tab&gt;` key ([0892db4](https://github.com/folke/snacks.nvim/commit/0892db4f42fc538df0a0b8fd66600d1e2d41b9e4))
* **picker.grep:** added `ft` (rg's `type`) and `regex` (rg's `--fixed-strings`) options ([0437cfd](https://github.com/folke/snacks.nvim/commit/0437cfd98ea9767836685ef8f100b7a758239624))
* **picker.list:** added debug option to show scores ([821e231](https://github.com/folke/snacks.nvim/commit/821e23101fdfcc28819e27596177eaa64eebf0c2))
* **picker.list:** added select_all action mapped to ctrl+a ([c9e2695](https://github.com/folke/snacks.nvim/commit/c9e2695969687285fbf53c86336b75c4dae3b609))
* **picker.list:** better way of highlighting field patterns ([924a988](https://github.com/folke/snacks.nvim/commit/924a988d9af72bf1abba122fa9f02a4eb917f15a))
* **picker.list:** make `conceallevel` configurable. Fixes [#635](https://github.com/folke/snacks.nvim/issues/635) ([d88eab6](https://github.com/folke/snacks.nvim/commit/d88eab6e3fec20e162f52e618114b869f561e3fd))
* **picker.lsp:** added `lsp_workspace_symbols`. Supports live search. Closes [#473](https://github.com/folke/snacks.nvim/issues/473) ([348307a](https://github.com/folke/snacks.nvim/commit/348307a82e4ae139fcb02b4cd4ae95dbf46f32c7))
* **picker.matcher:** added opts.matcher.sort_empty and opts.matcher.filename_bonus ([ed91078](https://github.com/folke/snacks.nvim/commit/ed91078625996106ddd31dfb4bac634d2895f61f))
* **picker.matcher:** better scoring algorithm based on fzf. Closes [#512](https://github.com/folke/snacks.nvim/issues/512). Fixes [#513](https://github.com/folke/snacks.nvim/issues/513) ([e4e2e88](https://github.com/folke/snacks.nvim/commit/e4e2e88c769d54094194d6e3d68fbfae87b20cbe))
* **picker.matcher:** integrate custom item scores ([7267e24](https://github.com/folke/snacks.nvim/commit/7267e2493b5962a550d874f142aaf64c3873fb7e))
* **picker.matcher:** moved length tiebreak to sorter instead ([d5ccb30](https://github.com/folke/snacks.nvim/commit/d5ccb301c1fe2adb874dd8f4f675797d983a8284))
* **picker.recent:** include open files in recent files. Closes [#487](https://github.com/folke/snacks.nvim/issues/487) ([96ffaba](https://github.com/folke/snacks.nvim/commit/96ffaba71bed87cf8bf75c6d945dedae3fa40af2))
* **picker.score:** prioritize matches in filenames ([5cf5ec1](https://github.com/folke/snacks.nvim/commit/5cf5ec1a314b38d4e361f7f26cb6eb14febd4d69))
* **picker.smart:** better frecency bonus ([74feefc](https://github.com/folke/snacks.nvim/commit/74feefc52284e2ebf93ad815ec5aaeec918d4dc2))
* **picker.sort:** default sorter can now sort by len of a field ([6ae87d9](https://github.com/folke/snacks.nvim/commit/6ae87d9f62a17124db9283c789b1bd968a55a85a))
* **picker.sources:** lines just sorts by score/idx. Smart sorts on empty ([be42182](https://github.com/folke/snacks.nvim/commit/be421822d5498ad962481b134e6272defff9118e))
* **picker:** add qflist_all action to send all even when already sel… ([#600](https://github.com/folke/snacks.nvim/issues/600)) ([c7354d8](https://github.com/folke/snacks.nvim/commit/c7354d83486d60d0a965426fa920d341759b69c6))
* **picker:** add some source aliases like the Telescope / FzfLua names ([5a83a8e](https://github.com/folke/snacks.nvim/commit/5a83a8e32885d6b923319cb8dc5ff1d1d97d0b10))
* **picker:** added `{preview}` and `{flags}` title placeholders. Closes [#557](https://github.com/folke/snacks.nvim/issues/557), Closes [#540](https://github.com/folke/snacks.nvim/issues/540) ([2e70b7f](https://github.com/folke/snacks.nvim/commit/2e70b7f42364e50df25407ddbd897e157a44c526))
* **picker:** added `git_branches` picker. Closes [#614](https://github.com/folke/snacks.nvim/issues/614) ([8563dfc](https://github.com/folke/snacks.nvim/commit/8563dfce682eeb260fa17e554b3e02de47e61f35))
* **picker:** added `inspect` action mapped to `&lt;c-i&gt;`. Useful to see what search fields are available on an item. ([2ba165b](https://github.com/folke/snacks.nvim/commit/2ba165b826d31ab0ebeaaff26632efe7013042b6))
* **picker:** added `smart` picker ([772f3e9](https://github.com/folke/snacks.nvim/commit/772f3e9b8970123db4050e9f7a5bdf2270575c6c))
* **picker:** added exclude option for files and grep. Closes [#581](https://github.com/folke/snacks.nvim/issues/581) ([192fb31](https://github.com/folke/snacks.nvim/commit/192fb31c4beda9aa4ebbc8bad0abe59df8bdde85))
* **picker:** added jump options jumplist(true for all), reuse_win & tagstack (true for lsp). Closes [#589](https://github.com/folke/snacks.nvim/issues/589). Closes [#568](https://github.com/folke/snacks.nvim/issues/568) ([84c3738](https://github.com/folke/snacks.nvim/commit/84c3738fb04fff83aba8e91c3af8ad9e74b089fd))
* **picker:** added preliminary support for combining finder results. More info coming soon ([000db17](https://github.com/folke/snacks.nvim/commit/000db17bf9f8bd243bbe944c0ae7e162d8cad572))
* **picker:** added spelling picker. Closes [#625](https://github.com/folke/snacks.nvim/issues/625) ([b170ced](https://github.com/folke/snacks.nvim/commit/b170ced527c03911a4658fb2df7139fa7040bcef))
* **picker:** added support for live args for `grep` and `files`. Closes [#601](https://github.com/folke/snacks.nvim/issues/601) ([50f3c3e](https://github.com/folke/snacks.nvim/commit/50f3c3e5b1c52c223a2689b1b2828c1ddae9e866))
* **picker:** added toggle/flag/action for `follow`. Closes [#633](https://github.com/folke/snacks.nvim/issues/633) ([aa53f6c](https://github.com/folke/snacks.nvim/commit/aa53f6c0799f1f6b80f6fb46472ec4773763f6b8))
* **picker:** allow disabling file icons ([76fbf9e](https://github.com/folke/snacks.nvim/commit/76fbf9e8a85485abfe1c53d096c74faad3fa2a6b))
* **picker:** allow setting a custom `opts.title`. Fixes [#620](https://github.com/folke/snacks.nvim/issues/620) ([6001fb2](https://github.com/folke/snacks.nvim/commit/6001fb2077306655afefba6593ec8b55e18abc39))
* **picker:** custom icon for unselected entries ([#588](https://github.com/folke/snacks.nvim/issues/588)) ([6402687](https://github.com/folke/snacks.nvim/commit/64026877ad8dac658eb5056e0c56f66e17401bdb))
* **picker:** restore cursor / topline on resume ([ca54948](https://github.com/folke/snacks.nvim/commit/ca54948f79917113dfcdf1c4ccaec573244a02aa))
* **pickers.format:** added `opts.picker.formatters.file.filename_first` ([98562ae](https://github.com/folke/snacks.nvim/commit/98562ae6a112bf1d80a9bec7fb2849605234a9d5))
* **picker:** use an sqlite3 database for frecency data when available ([c43969d](https://github.com/folke/snacks.nvim/commit/c43969dabd42e261c570f533c2f343f99a9d1f01))
* **scroll:** faster animations for scroll repeats after delay. (replaces spamming handling) ([d494a9e](https://github.com/folke/snacks.nvim/commit/d494a9e66447e9ae22e40c374e2e7d9a24b64d93))
* **snacks:** added `snacks.picker` ([#445](https://github.com/folke/snacks.nvim/issues/445)) ([559d6c6](https://github.com/folke/snacks.nvim/commit/559d6c6bf207e4e768a88e7f727ac12a87c768b7))
* **toggle:** allow toggling global options. Fixes [#534](https://github.com/folke/snacks.nvim/issues/534) ([b50effc](https://github.com/folke/snacks.nvim/commit/b50effc96763f0b84473b68c733ef3eff8a14be5))
* **win:** warn on duplicate keymaps that differ in case. See [#554](https://github.com/folke/snacks.nvim/issues/554) ([a71b7c0](https://github.com/folke/snacks.nvim/commit/a71b7c0d26b578ad2b758ad74139b2ddecf8c15f))


### Bug Fixes

* **animate:** never animate stopped animations ([197b0a9](https://github.com/folke/snacks.nvim/commit/197b0a9be93a6fa49b840fe159ce6373c7edcf98))
* **bigfile:** check existence of NoMatchParen before executing ([#561](https://github.com/folke/snacks.nvim/issues/561)) ([9b8f57b](https://github.com/folke/snacks.nvim/commit/9b8f57b96f823b83848572bf3384754f8ab46217))
* **config:** better vim.tbl_deep_extend that prevents issues with list-like tables. Fixes [#554](https://github.com/folke/snacks.nvim/issues/554) ([75eb16f](https://github.com/folke/snacks.nvim/commit/75eb16fd9c746bbd5e21992b6eb986d389dd246e))
* **config:** dont exclude metatables ([2d4a0b5](https://github.com/folke/snacks.nvim/commit/2d4a0b594a69c535704c15fc41c74d18c5f4d08b))
* **grep:** explicitely set `--no-hidden` because of the git filter ([ae2de9a](https://github.com/folke/snacks.nvim/commit/ae2de9aa8101dbff1ee1ab101d53e916f6e217dd))
* **indent:** dont redraw when list/shiftwidth/listchars change. Triggered way too often. Fixes [#613](https://github.com/folke/snacks.nvim/issues/613). Closes [#627](https://github.com/folke/snacks.nvim/issues/627) ([d212e3c](https://github.com/folke/snacks.nvim/commit/d212e3c99090eec3211e84e526b9fbdd000e020c))
* **input:** bring back `&lt;c-w&gt;`. Fixes [#426](https://github.com/folke/snacks.nvim/issues/426). Closes [#429](https://github.com/folke/snacks.nvim/issues/429) ([5affa72](https://github.com/folke/snacks.nvim/commit/5affa7214f621144526b9a7d93b83302fa6ea6f4))
* **layout:** allow root with relative=cursor. Closes [#479](https://github.com/folke/snacks.nvim/issues/479) ([f06f14c](https://github.com/folke/snacks.nvim/commit/f06f14c4ae4d131eb5e15f4c49994f8debddff42))
* **layout:** don't trigger events during re-layout on resize. Fixes [#552](https://github.com/folke/snacks.nvim/issues/552) ([d73a4a6](https://github.com/folke/snacks.nvim/commit/d73a4a64dfd203b9f3a4a9dedd76af398faa6652))
* **layout:** open/update windows in order of the layout to make sure offsets are correct ([034d50d](https://github.com/folke/snacks.nvim/commit/034d50d44e98af433260292001a88ac54d2466b6))
* **layout:** use eventignore when updating windows that are already visible to fix issues with synatx. Fixes [#552](https://github.com/folke/snacks.nvim/issues/552) ([f7d967c](https://github.com/folke/snacks.nvim/commit/f7d967c5157ee621168153502812a266c509bd97))
* **lsp:** use treesitter highlights for LSP locations ([fc06a36](https://github.com/folke/snacks.nvim/commit/fc06a363b95312eba0f3335f1190c745d0e5ea26))
* **notifier:** content width. Fixes [#631](https://github.com/folke/snacks.nvim/issues/631) ([0e27737](https://github.com/folke/snacks.nvim/commit/0e277379ea7d25c97d109d31da33abacf26da841))
* **picker.actions:** added hack to make folds work. Fixes [#514](https://github.com/folke/snacks.nvim/issues/514) ([df1060f](https://github.com/folke/snacks.nvim/commit/df1060fa501d06758d588a341d5cdec650cbfc67))
* **picker.actions:** close existing empty buffer if it's the current buffer ([0745505](https://github.com/folke/snacks.nvim/commit/0745505f2f43d2983867f48805bd4f700ad06c73))
* **picker.actions:** full path for qflist and loclist actions ([3e39250](https://github.com/folke/snacks.nvim/commit/3e392507963f784a4d57708585f8e012f1b95768))
* **picker.actions:** only delete empty buffer if it's not displayed in a window. Fixes [#566](https://github.com/folke/snacks.nvim/issues/566) ([b7ab888](https://github.com/folke/snacks.nvim/commit/b7ab888dd0c5bb0bafe9d01209f6a63320621b11))
* **picker.actions:** return action result. Fixes [#612](https://github.com/folke/snacks.nvim/issues/612). See [#611](https://github.com/folke/snacks.nvim/issues/611) ([4a482be](https://github.com/folke/snacks.nvim/commit/4a482bea3c5cac7af66a7a3d5cee5f97fca6c9d8))
* **picker.colorscheme:** nil check. Fixes [#575](https://github.com/folke/snacks.nvim/issues/575) ([de01907](https://github.com/folke/snacks.nvim/commit/de01907930bb125d1b67b4a1fb372f21d972f70b))
* **picker.config:** allow merging list-like layouts with table layout options ([706b1ab](https://github.com/folke/snacks.nvim/commit/706b1abc1697ca050314dc667e0900d53cad8aa4))
* **picker.config:** better config merging and tests ([9986b47](https://github.com/folke/snacks.nvim/commit/9986b47707bbe76cf3b901c3048e55b2ba2bb4a8))
* **picker.config:** normalize keys before merging so you can override `&lt;c-s&gt;` with `<C-S>` ([afef949](https://github.com/folke/snacks.nvim/commit/afef949d88b6fa3dde8515b27066b132cfdb0a70))
* **picker.db:** remove tests ([71f69e5](https://github.com/folke/snacks.nvim/commit/71f69e5e57f355f40251e274d45560af7d8dd365))
* **picker.diagnostics:** sort on empty pattern. Fixes [#641](https://github.com/folke/snacks.nvim/issues/641) ([c6c76a6](https://github.com/folke/snacks.nvim/commit/c6c76a6aa338af47e2725cff35cc814fcd2ad1e7))
* **picker.files:** ignore errors since it's not possible to know if the error isbecause of an incomplete pattern. Fixes [#551](https://github.com/folke/snacks.nvim/issues/551) ([4823f2d](https://github.com/folke/snacks.nvim/commit/4823f2da65a5e11c10242af5e0d1c977474288b3))
* **picker.format:** filename ([a194bbc](https://github.com/folke/snacks.nvim/commit/a194bbc3747f73416ec2fd25cb39c233fcc7a656))
* **picker.format:** use forward slashes for paths. Closes [#624](https://github.com/folke/snacks.nvim/issues/624) ([5f82ffd](https://github.com/folke/snacks.nvim/commit/5f82ffde0befbcbfbedb9b5066bf4f3a5d667495))
* **picker.git:** git log file/line for a file not in cwd. Fixes [#616](https://github.com/folke/snacks.nvim/issues/616) ([9034319](https://github.com/folke/snacks.nvim/commit/903431903b0151f97f45087353ebe8fa1cb8ef80))
* **picker.git:** git_file and git_line should only show diffs including the file. Fixes [#522](https://github.com/folke/snacks.nvim/issues/522) ([1481a90](https://github.com/folke/snacks.nvim/commit/1481a90affedb24291997f5ebde0637cafc1d20e))
* **picker.git:** use Snacks.git.get_root instead vim.fs.root for backward compatibility ([a2fb70e](https://github.com/folke/snacks.nvim/commit/a2fb70e8ba2bb2ce5c60ed1ee7505d6f6d7be061))
* **picker.highlight:** properly deal with multiline treesitter captures ([27b72ec](https://github.com/folke/snacks.nvim/commit/27b72ecd005743ecf5855cd3b430fce74bd4f2e3))
* **picker.input:** don't set prompt interrupt, but use a `&lt;c-c&gt;` mapping instead that can be changed ([123f0d9](https://github.com/folke/snacks.nvim/commit/123f0d9e5d7be5b23ef9b28b9ddde403e4b2d061))
* **picker.input:** leave insert mode when closing and before executing confirm. Fixes [#543](https://github.com/folke/snacks.nvim/issues/543) ([05eb22c](https://github.com/folke/snacks.nvim/commit/05eb22c4fbe09f9bed53a752301d5c2a8a060a4e))
* **picker.input:** statuscolumn on resize / re-layout. Fixes [#643](https://github.com/folke/snacks.nvim/issues/643) ([4d8d844](https://github.com/folke/snacks.nvim/commit/4d8d844027a3ea04ac7ecb0ab6cd63e39e96d06f))
* **picker.input:** strip newllines from pattern (mainly due to pasting in the input box) ([c6a9955](https://github.com/folke/snacks.nvim/commit/c6a9955516b686d1b6bd815e1df0c808baa60bd3))
* **picker.input:** use `Snacks.util.wo` instead of `vim.wo`. Fixes [#643](https://github.com/folke/snacks.nvim/issues/643) ([d6284d5](https://github.com/folke/snacks.nvim/commit/d6284d51ff748f43c5431c35ffed7f7c02e51069))
* **picker.list:** disable folds ([5582a84](https://github.com/folke/snacks.nvim/commit/5582a84020a1e11d9001660252cdee6a424ba159))
* **picker.list:** include `search` filter for highlighting items (live search). See [#474](https://github.com/folke/snacks.nvim/issues/474) ([1693fbb](https://github.com/folke/snacks.nvim/commit/1693fbb0dcf1b82f2212a325379ebb0257d582e5))
* **picker.list:** newlines in text. Fixes [#607](https://github.com/folke/snacks.nvim/issues/607). Closes [#580](https://github.com/folke/snacks.nvim/issues/580) ([c45a940](https://github.com/folke/snacks.nvim/commit/c45a94044b8e4c9f5deb420a7caa505281e1eed5))
* **picker.list:** possible issue with window options being set in the wrong window ([f1b6c55](https://github.com/folke/snacks.nvim/commit/f1b6c55027c6e75940fcb40fa8ac5ab717de1647))
* **picker.list:** scores debug ([9499b94](https://github.com/folke/snacks.nvim/commit/9499b944e79ff305769a59819c44a93911023fc7))
* **picker.lsp:** added support for single location result ([79d27f1](https://github.com/folke/snacks.nvim/commit/79d27f19dc62c0978d2688c6fac9348f253ef007))
* **picker.matcher:** initialize matcher with pattern from opts. Fixes [#596](https://github.com/folke/snacks.nvim/issues/596) ([c434eb8](https://github.com/folke/snacks.nvim/commit/c434eb89fe030672f4e518b4de1e506ffaf0e96e))
* **picker.matcher:** inverse scores ([1816931](https://github.com/folke/snacks.nvim/commit/1816931aadb1fdcd3e08606d773d31f3d51fabcc))
* **picker.minheap:** clear sorted on minheap clear. Fixes [#492](https://github.com/folke/snacks.nvim/issues/492) ([79bea58](https://github.com/folke/snacks.nvim/commit/79bea58b1ec92909d763c8b5788baf8da6c19a06))
* **picker.preview:** don't show line numbers for preview commands ([a652214](https://github.com/folke/snacks.nvim/commit/a652214f52694233b5e27d374db0d51d2f7cb43d))
* **picker.preview:** pattern to detect binary files was incorrect ([bbd1a08](https://github.com/folke/snacks.nvim/commit/bbd1a0885b3e89103a8a59f1f07d296f23c7d2ad))
* **picker.preview:** scratch buffer filetype. Fixes [#595](https://github.com/folke/snacks.nvim/issues/595) ([ece76b3](https://github.com/folke/snacks.nvim/commit/ece76b333a9ff372959bf5204ab22f58215383c1))
* **picker.proc:** correct offset for carriage returns. Fixes [#599](https://github.com/folke/snacks.nvim/issues/599) ([a01e0f5](https://github.com/folke/snacks.nvim/commit/a01e0f536815a0cc23443fae5ac10f7fdcb4f313))
* **picker.qf:** better quickfix item list. Fixes [#562](https://github.com/folke/snacks.nvim/issues/562) ([cb84540](https://github.com/folke/snacks.nvim/commit/cb845408dbc795582ea1567fba2bf4ba64c777ac))
* **picker.select:** allow main to be current. Fixes [#497](https://github.com/folke/snacks.nvim/issues/497) ([076259d](https://github.com/folke/snacks.nvim/commit/076259d263c927ed1d1c56c94b6e870230e77a3d))
* **picker.util:** cleanup func for key-value store (frecency) ([bd2da45](https://github.com/folke/snacks.nvim/commit/bd2da45c384ea7ce44bdd15a7b5e32ee3806cf8d))
* **picker:** add alias for `oldfiles` ([46554a6](https://github.com/folke/snacks.nvim/commit/46554a63425c0594eacaa0e8eaddec5dbf79b48e))
* **picker:** add keymaps for preview scratch buffers ([dc3f114](https://github.com/folke/snacks.nvim/commit/dc3f114c1f787218e4314d907866874a62253756))
* **picker:** always stopinsert, even when picker is already closed. Should not be needed, but some plugins misbehave. See  [#579](https://github.com/folke/snacks.nvim/issues/579) ([29becb0](https://github.com/folke/snacks.nvim/commit/29becb0ecbeb20683b3ae71d4c082734b2f518af))
* **picker:** better buffer edit. Fixes [#593](https://github.com/folke/snacks.nvim/issues/593) ([716492c](https://github.com/folke/snacks.nvim/commit/716492c57870e11e04a5764bcc1e549859f563be))
* **picker:** better normkey. Fixes [#610](https://github.com/folke/snacks.nvim/issues/610) ([540ecbd](https://github.com/folke/snacks.nvim/commit/540ecbd9a4b4c4d4ed47db83367f9e5d04220c27))
* **picker:** changed inspect mapping to `&lt;a-d&gt;` since not all terminal differentiate between `<a-i>` and `<tab>` ([8386540](https://github.com/folke/snacks.nvim/commit/8386540c422774059a75fe26ce7cfb6ab3811c73))
* **picker:** correctly normalize path after fnamemodify ([f351dcf](https://github.com/folke/snacks.nvim/commit/f351dcfcaca069d5f70bcf6edbde244e7358d063))
* **picker:** deepcopy before config merging. Fixes [#554](https://github.com/folke/snacks.nvim/issues/554) ([7865df0](https://github.com/folke/snacks.nvim/commit/7865df0558fa24cce9ec27c4e002d5e179cab685))
* **picker:** don't throttle preview if it's the first item we're previewing ([b785167](https://github.com/folke/snacks.nvim/commit/b785167814c5481643b53d241487fc9802a1ab13))
* **picker:** dont fast path matcher when finder items have scores ([2ba5602](https://github.com/folke/snacks.nvim/commit/2ba5602834830e1a96e4f1a81e0fbb310481ca74))
* **picker:** format: one too many spaces for default icon in results … ([#594](https://github.com/folke/snacks.nvim/issues/594)) ([d7f727f](https://github.com/folke/snacks.nvim/commit/d7f727f673a67b60882a3d2a96db4d1490566e73))
* **picker:** picker:items() should return filtered items, not finder items. Closes [#481](https://github.com/folke/snacks.nvim/issues/481) ([d67e093](https://github.com/folke/snacks.nvim/commit/d67e093bbc2a2069c5e6118babf99c9029b34675))
* **picker:** potential issue with preview winhl being set on the main window ([34208eb](https://github.com/folke/snacks.nvim/commit/34208ebe00a237a232a6050f81fb89f25d473180))
* **picker:** preview / lsp / diagnostics positions were wrong; Should all be (1-0) indexed. Fixes [#543](https://github.com/folke/snacks.nvim/issues/543) ([40d08bd](https://github.com/folke/snacks.nvim/commit/40d08bd901db740f4b270fb6e9b710312a2846df))
* **picker:** properly handle `opts.layout` being a string. Fixes [#636](https://github.com/folke/snacks.nvim/issues/636) ([b80c9d2](https://github.com/folke/snacks.nvim/commit/b80c9d275d05778c90df32bd361c8630b28d13fd))
* **picker:** select_and_prev should use list_up instead of list_down ([#471](https://github.com/folke/snacks.nvim/issues/471)) ([b993be7](https://github.com/folke/snacks.nvim/commit/b993be762ba9aa9c1efd858ae777eef3de28c609))
* **picker:** set correct cwd for git status picker ([#505](https://github.com/folke/snacks.nvim/issues/505)) ([2cc7cf4](https://github.com/folke/snacks.nvim/commit/2cc7cf42e94041c08f5654935f745424d10a15b1))
* **picker:** show all files in git status ([#586](https://github.com/folke/snacks.nvim/issues/586)) ([43c312d](https://github.com/folke/snacks.nvim/commit/43c312dfc1433b3bdf24c7a14bc0aac648fa8d33))
* **scope:** make sure to parse the ts tree. Fixes [#521](https://github.com/folke/snacks.nvim/issues/521) ([4c55f1c](https://github.com/folke/snacks.nvim/commit/4c55f1c2da103e4c2776274cb0f6fab0318ea811))
* **scratch:** autowrite right buffer. Fixes [#449](https://github.com/folke/snacks.nvim/issues/449). ([#452](https://github.com/folke/snacks.nvim/issues/452)) ([8d2e26c](https://github.com/folke/snacks.nvim/commit/8d2e26cf27330ead517193f7eeb898d189a973c2))
* **scroll:** don't animate for new changedtick. Fixes [#384](https://github.com/folke/snacks.nvim/issues/384) ([ac8b3cd](https://github.com/folke/snacks.nvim/commit/ac8b3cdfa08c2bba065c95b512887d3bbea91807))
* **scroll:** don't animate when recording or executing macros ([7dcdcb0](https://github.com/folke/snacks.nvim/commit/7dcdcb0b6ab6ecb2c6efdbaa4769bc03f5837832))
* **statuscolumn:** return "" when no signs and no numbers are needed. Closes [#570](https://github.com/folke/snacks.nvim/issues/570). ([c4980ef](https://github.com/folke/snacks.nvim/commit/c4980ef9b4e678d4057ece6fd2c13637a395f3a0))
* **util:** normkey ([cd58a14](https://github.com/folke/snacks.nvim/commit/cd58a14e20fdcd810b55e8aee535486a3ad8719f))
* **win:** clear syntax when setting filetype ([c49f38c](https://github.com/folke/snacks.nvim/commit/c49f38c5a919400689ab11669d45331f210ea91c))
* **win:** correctly deal with initial text containing newlines. Fixes [#542](https://github.com/folke/snacks.nvim/issues/542) ([825c106](https://github.com/folke/snacks.nvim/commit/825c106f40352dc2979724643e0d36af3a962eb1))
* **win:** duplicate keymap should take mode into account. Closes [#559](https://github.com/folke/snacks.nvim/issues/559) ([097e68f](https://github.com/folke/snacks.nvim/commit/097e68fc720a8fcdb370e6ccd2a3acad40b98952))
* **win:** exclude cursor from redraw. Fixes [#613](https://github.com/folke/snacks.nvim/issues/613) ([ad9b382](https://github.com/folke/snacks.nvim/commit/ad9b382f7d0e150d2420cb65d44d6fb81a6b62c8))
* **win:** fix relative=cursor again ([5b1cd46](https://github.com/folke/snacks.nvim/commit/5b1cd464e8759156d4d69f0398a0f5b34fa0b743))
* **win:** relative=cursor. Closes [#427](https://github.com/folke/snacks.nvim/issues/427). Closes [#477](https://github.com/folke/snacks.nvim/issues/477) ([743f8b3](https://github.com/folke/snacks.nvim/commit/743f8b3fee780780d8e201960a4a295dc5187e9b))
* **win:** special handling of `&lt;C-J&gt;`. Closes [#565](https://github.com/folke/snacks.nvim/issues/565). Closes [#592](https://github.com/folke/snacks.nvim/issues/592) ([5ac80f0](https://github.com/folke/snacks.nvim/commit/5ac80f0159239e44448fa9b08d8462e93342192d))
* **win:** win position with border offsets. Closes [#413](https://github.com/folke/snacks.nvim/issues/413). Fixes [#423](https://github.com/folke/snacks.nvim/issues/423) ([ee08b1f](https://github.com/folke/snacks.nvim/commit/ee08b1f32e06904318f8fa24714557d3abcdd215))
* **words:** added support for new name of the namespace used for lsp references. Fixes [#555](https://github.com/folke/snacks.nvim/issues/555) ([566f302](https://github.com/folke/snacks.nvim/commit/566f3029035143c525c0df7f5e7bbf9b3e939f54))


### Performance Improvements

* **notifier:** skip processing during search. See [#627](https://github.com/folke/snacks.nvim/issues/627) ([cf5f56a](https://github.com/folke/snacks.nvim/commit/cf5f56a1d82f4ece762843334c744e5830f4b4ef))
* **picker.matcher:** fast path when we already found a perfect match ([6bbf50c](https://github.com/folke/snacks.nvim/commit/6bbf50c5e3a3ab3bfc6a6747f6a2c66cbc9b7548))
* **picker.matcher:** only use filename_bonus for items that have a file field ([fd854ab](https://github.com/folke/snacks.nvim/commit/fd854ab9efdd13cf9cda192838f967551f332e36))
* **picker.matcher:** yield every 1ms to prevent ui locking in large repos ([19979c8](https://github.com/folke/snacks.nvim/commit/19979c88f37930f71bd98b96e1afa50ae26a09ae))
* **picker.util:** cache path calculation ([7117356](https://github.com/folke/snacks.nvim/commit/7117356b49ceedd7185c43a732dfe10c6a60cdbc))
* **picker:** dont use prompt buffer callbacks ([8293add](https://github.com/folke/snacks.nvim/commit/8293add1e524f48aee1aacd68f72d4204096aed2))
* **picker:** matcher optims ([5295741](https://github.com/folke/snacks.nvim/commit/529574128783c0fc4a146b207ea532950f48732f))

## [2.12.0](https://github.com/folke/snacks.nvim/compare/v2.11.0...v2.12.0) (2025-01-05)


### Features

* **debug:** system & memory metrics useful for debugging ([cba16bd](https://github.com/folke/snacks.nvim/commit/cba16bdb35199c941c8d78b8fb9ddecf568c0b1f))
* **input:** disable completion engines in input ([37038df](https://github.com/folke/snacks.nvim/commit/37038df00d6b47a65de24266c25683ff5a781a40))
* **scope:** disable treesitter blocks by default ([8ec6e6a](https://github.com/folke/snacks.nvim/commit/8ec6e6adc5b098674c41005530d1c8af126480ae))
* **statuscolumn:** allow changing the marks hl group. Fixes [#390](https://github.com/folke/snacks.nvim/issues/390) ([8a6e5b9](https://github.com/folke/snacks.nvim/commit/8a6e5b9685bb8c596a179256b048043a51a64e09))
* **util:** `Snacks.util.ref` ([7383eda](https://github.com/folke/snacks.nvim/commit/7383edaec842609deac50b114a3567c2983b54f4))
* **util:** throttle ([737980d](https://github.com/folke/snacks.nvim/commit/737980d987cdb4d3c2b18e0b3b8613fde974a2e9))
* **win:** `Snacks.win:border_size` ([4cd0647](https://github.com/folke/snacks.nvim/commit/4cd0647eb5bda07431e125374c1419059783a741))
* **win:** `Snacks.win:redraw` ([0711a82](https://github.com/folke/snacks.nvim/commit/0711a82b7a77c0ab35251e28cf1a7be0b3bde6d4))
* **win:** `Snacks.win:scroll` ([a1da66e](https://github.com/folke/snacks.nvim/commit/a1da66e3bf2768273f1dfb556b29269fd8ba153d))
* **win:** allow setting `desc` for window actions ([402494b](https://github.com/folke/snacks.nvim/commit/402494bdee8800c8ac3eeceb8c5e78e00f72f265))
* **win:** better dimension calculation for windows (use by upcoming layouts) ([cc0b528](https://github.com/folke/snacks.nvim/commit/cc0b52872b99e3af7d80536e8a9cbc28d47e7f19))
* **win:** top,right,bottom,left borders ([320ecbc](https://github.com/folke/snacks.nvim/commit/320ecbc15c25a240fee2c2970f826259d809ed72))


### Bug Fixes

* **dashboard:** hash dashboard terminal section caching key to support long commands ([#381](https://github.com/folke/snacks.nvim/issues/381)) ([d312053](https://github.com/folke/snacks.nvim/commit/d312053f78b4fb55523def179ac502438dd93193))
* **debug:** make debug.inpect work in fast events ([b70edc2](https://github.com/folke/snacks.nvim/commit/b70edc29dbc8c9718af246a181b05d4d190ad260))
* **debug:** make sure debug can be required in fast events ([6cbdbb9](https://github.com/folke/snacks.nvim/commit/6cbdbb9afa748e84af4c35d17fc4737b18638a35))
* **indent:** allow rendering over blank lines. Fixes [#313](https://github.com/folke/snacks.nvim/issues/313) ([766e671](https://github.com/folke/snacks.nvim/commit/766e67145259e30ae7d63dfd6d51b8d8ef0840ae))
* **indent:** better way to deal with `breakindent`. Fixes [#329](https://github.com/folke/snacks.nvim/issues/329) ([235427a](https://github.com/folke/snacks.nvim/commit/235427abcbf3e2b251a8b75f0e409dfbb6c737d6))
* **indent:** breakdinent ([972c61c](https://github.com/folke/snacks.nvim/commit/972c61cc1cd254ef3b43ec1dfd51eefbdc441a7d))
* **indent:** correct calculation of partial indent when leftcol &gt; 0 ([6f3cbf8](https://github.com/folke/snacks.nvim/commit/6f3cbf8ad328d181a694cdded344477e81cd094d))
* **indent:** do animate check in bufcall ([c62e7a2](https://github.com/folke/snacks.nvim/commit/c62e7a2561351c9fe3a8e7e9fc8602f3b61abf53))
* **indent:** don't render scopes in closed folds. Fixes [#352](https://github.com/folke/snacks.nvim/issues/352) ([94ec568](https://github.com/folke/snacks.nvim/commit/94ec5686a64218c9477de7761af4fd34dd4a665b))
* **indent:** off-by-one for indent guide hl group ([551e644](https://github.com/folke/snacks.nvim/commit/551e644ca311d065b3a6882db900846c1e66e636))
* **indent:** repeat_linbebreak only works on Neovim &gt;= 0.10. Fixes [#353](https://github.com/folke/snacks.nvim/issues/353) ([b93201b](https://github.com/folke/snacks.nvim/commit/b93201bdf36bd62b07daf7d40bc305998f9da52c))
* **indent:** simplify indent guide logic and never overwrite blanks. Fixes [#334](https://github.com/folke/snacks.nvim/issues/334) ([282be8b](https://github.com/folke/snacks.nvim/commit/282be8bfa8e6f46d6994ff46638d1c155b90753f))
* **indent:** typo for underline ([66cce2f](https://github.com/folke/snacks.nvim/commit/66cce2f512e11a961a8f187eac802acbf8725d05))
* **indent:** use space instead of full blank for indent offset. See [#313](https://github.com/folke/snacks.nvim/issues/313) ([58081bc](https://github.com/folke/snacks.nvim/commit/58081bcecb31db8c6f12ad876c70786582a7f6a8))
* **input:** change buftype to prompt. Fixes [#350](https://github.com/folke/snacks.nvim/issues/350) ([2990bf0](https://github.com/folke/snacks.nvim/commit/2990bf0c7a79f5780a0268a47bae69ef004cec99))
* **input:** make sure to show input window with a higher zindex of the parent window (if float) ([3123e6e](https://github.com/folke/snacks.nvim/commit/3123e6e9882f178411ea6e9fbf5e9552134b82b0))
* **input:** refactor for win changes and ensure modified=false. Fixes [#403](https://github.com/folke/snacks.nvim/issues/403). Fixes [#402](https://github.com/folke/snacks.nvim/issues/402) ([8930630](https://github.com/folke/snacks.nvim/commit/89306308f357e12510683758c35d08f368db2b2c))
* **input:** use correct highlight group for input prompt ([#328](https://github.com/folke/snacks.nvim/issues/328)) ([818da33](https://github.com/folke/snacks.nvim/commit/818da334ac8f655235b5861bb50577921e4e6bd8))
* **lazygit:** enable boolean values in config ([#377](https://github.com/folke/snacks.nvim/issues/377)) ([ec34684](https://github.com/folke/snacks.nvim/commit/ec346843e0adb51b45e595dd0ef34bf9e64d4627))
* **notifier:** open history window with correct style ([#307](https://github.com/folke/snacks.nvim/issues/307)) ([d2b5680](https://github.com/folke/snacks.nvim/commit/d2b5680359ee8feb34b095fd574b4f9b3f013629))
* **notifier:** rename style `notification.history` -&gt; `notification_history` ([fd9ef30](https://github.com/folke/snacks.nvim/commit/fd9ef30206185e3dd4d3294c74e2fd0dee9722d1))
* **scope:** allow treesitter scopes when treesitter highlighting is disabled. See [#231](https://github.com/folke/snacks.nvim/issues/231) ([58ae580](https://github.com/folke/snacks.nvim/commit/58ae580c2c12275755bb3e2003aebd06d550f2db))
* **scope:** don't expand to invalid range. Fixes [#339](https://github.com/folke/snacks.nvim/issues/339) ([1244305](https://github.com/folke/snacks.nvim/commit/1244305bedb8e60a946d949c78453263a714a4ad))
* **scope:** properly caluclate start indent when `cursor=true` for indent scopes. See [#5068](https://github.com/folke/snacks.nvim/issues/5068) ([e63fa7b](https://github.com/folke/snacks.nvim/commit/e63fa7bf05d22f4306c5fff594d48bc01e382238))
* **scope:** use virtcol for calculating scopes at the cursor ([6a36f32](https://github.com/folke/snacks.nvim/commit/6a36f32eaa7d5d59e681b7b8112a85a58a2d563d))
* **scroll:** check for invalid window. Fixes [#340](https://github.com/folke/snacks.nvim/issues/340) ([b6032e8](https://github.com/folke/snacks.nvim/commit/b6032e8f1b5cba55b5a2cf138ab4f172c4decfbd))
* **scroll:** don't animate when leaving cmdline search with incsearch enabled. Fixes [#331](https://github.com/folke/snacks.nvim/issues/331) ([fc0a99b](https://github.com/folke/snacks.nvim/commit/fc0a99b8493c34e6a930b3571ee8491e23831bca))
* **util:** throttle now autonatically schedules when in fast event ([9840331](https://github.com/folke/snacks.nvim/commit/98403313c749e26e5ae9a8ff51343c97f76ce170))
* **win:** backdrop having bright cell at top right ([#400](https://github.com/folke/snacks.nvim/issues/400)) ([373d0f9](https://github.com/folke/snacks.nvim/commit/373d0f9b6d6d83cdba641937b6303b0a0a18119f))
* **win:** don't enter when focusable is `false` ([ca233c7](https://github.com/folke/snacks.nvim/commit/ca233c7448c930658e8c7da9745e8d98884c3852))
* **win:** force-close any buffer that is not a file ([dd50e53](https://github.com/folke/snacks.nvim/commit/dd50e53a9efea11329e21c4a61ca35ae5122ceca))
* **win:** unset `winblend` when transparent ([0617e28](https://github.com/folke/snacks.nvim/commit/0617e28f8289002310fed5986acc29fde38e01b5))
* **words:** only check modes for `is_enabled` when needed ([80dcb88](https://github.com/folke/snacks.nvim/commit/80dcb88ede1a96f79edd3b7ede0bc41d51dd8a2d))
* **zen:** set zindex to 40, lower than hover (45). Closes [#345](https://github.com/folke/snacks.nvim/issues/345) ([05f4981](https://github.com/folke/snacks.nvim/commit/05f49814f3a2f3ecb83d9e72b7f8f2af40351aad))

## [2.11.0](https://github.com/folke/snacks.nvim/compare/v2.10.0...v2.11.0) (2024-12-15)


### Features

* **indent:** properly handle continuation indents. Closes [#286](https://github.com/folke/snacks.nvim/issues/286) ([f2bb7fa](https://github.com/folke/snacks.nvim/commit/f2bb7fa94e4b9b1fa7f84066bbedea8b3d9875e3))
* **input:** allow configuring position of prompt and icon ([d0cb707](https://github.com/folke/snacks.nvim/commit/d0cb7070e98d6a2ca31d94dd04d7048c9b258f33))
* **notifier:** notification `history` option ([#297](https://github.com/folke/snacks.nvim/issues/297)) ([8f56e19](https://github.com/folke/snacks.nvim/commit/8f56e19f916f8075e2bfb534d723e3d850e256a4))
* **scope:** `Scope:inner` for indent based and treesitter scopes ([8a8b1c9](https://github.com/folke/snacks.nvim/commit/8a8b1c976fc2736a3b91697750074fd3b23a24c9))
* **scope:** added `__tostring` for debugging ([94e0849](https://github.com/folke/snacks.nvim/commit/94e0849c3aae3b818cad2804c256c57318256c72))
* **scope:** added `opts.cursor` to take cursor column into account for scope detection. (defaults to true). Closes [#282](https://github.com/folke/snacks.nvim/issues/282) ([54bc6ba](https://github.com/folke/snacks.nvim/commit/54bc6bab2dbd07270c8c3fd447e8b72f825c315c))
* **scope:** text objects now use treesitter scopes by default. See [#231](https://github.com/folke/snacks.nvim/issues/231) ([a953697](https://github.com/folke/snacks.nvim/commit/a9536973a9111c3c7b66fb51bc5f62be27850884))
* **statuscolumn:** allow left/right to be a function. Closes [#288](https://github.com/folke/snacks.nvim/issues/288) ([cb42b95](https://github.com/folke/snacks.nvim/commit/cb42b952c5d4047f8e805c02c7aa596eb4e45ef2))
* **util:** on_key handler ([002d5eb](https://github.com/folke/snacks.nvim/commit/002d5eb5c2710a4e7456dd572543369e8424fd64))
* **win:** win:line() ([17494ad](https://github.com/folke/snacks.nvim/commit/17494ad9bf98e82c6a16f032cb3c9c82e072371a))


### Bug Fixes

* **dashboard:** telescope can't be run from a `vim.schedule` for some reason ([dcc5338](https://github.com/folke/snacks.nvim/commit/dcc5338e6f2a825b78791c96829d7e5a29e3ea5d))
* **indent:** `opts.indent.blank` now defaults to `listchars.space`. Closes [#291](https://github.com/folke/snacks.nvim/issues/291) ([31bc409](https://github.com/folke/snacks.nvim/commit/31bc409342b00d406963de3e1f38f3a2f84cfdcb))
* **indent:** fixup ([14d71c3](https://github.com/folke/snacks.nvim/commit/14d71c3fb2856634a8697f7c9f01704980e49bd0))
* **indent:** honor lead listchar ([#303](https://github.com/folke/snacks.nvim/issues/303)) ([7db0cc9](https://github.com/folke/snacks.nvim/commit/7db0cc9281b23c71155422433b6f485675674932))
* **indent:** honor listchars and list when blank is `nil`. Closes [#296](https://github.com/folke/snacks.nvim/issues/296) ([0e150f5](https://github.com/folke/snacks.nvim/commit/0e150f5510e381753ddd18f29facba14716d5669))
* **indent:** lower priorities of indent guides ([7f66818](https://github.com/folke/snacks.nvim/commit/7f668185ea810304cef5cb166a51665d4859124b))
* **input:** check if parent win still exists. Fixes [#287](https://github.com/folke/snacks.nvim/issues/287) ([db768a5](https://github.com/folke/snacks.nvim/commit/db768a5497301aad7fcddae2fe578cb320cc9ca2))
* **input:** go back to insert mode if input was started from insert mode. Fixes [#287](https://github.com/folke/snacks.nvim/issues/287) ([5d00e6d](https://github.com/folke/snacks.nvim/commit/5d00e6dec5686d7d2a6d97288287892d117d579b))
* **input:** missing padding if neither title nor icon positioned left ([#292](https://github.com/folke/snacks.nvim/issues/292)) ([97542a7](https://github.com/folke/snacks.nvim/commit/97542a7d9bfc7f242acbaa9851a62c649222fec8))
* **input:** open input window with `noautocmd=true` set. Fixes [#287](https://github.com/folke/snacks.nvim/issues/287) ([26b7d4c](https://github.com/folke/snacks.nvim/commit/26b7d4cbd9f803d9f759fc00d0dc4caa0141048b))
* **scope:** add `indent` to `__eq` ([be2779e](https://github.com/folke/snacks.nvim/commit/be2779e942bee0932e9c14ef4ed3e4002be861ce))
* **scope:** better treesitter scope edge detection ([b7355c1](https://github.com/folke/snacks.nvim/commit/b7355c16fb441e33be993ade74130464b62304cf))
* **scroll:** check mousescroll before spamming ([3d67bda](https://github.com/folke/snacks.nvim/commit/3d67bda1e29b8e8108dd74d611bf5c8b42883838))
* **util:** on_key compat with Neovim 0.9 ([effa885](https://github.com/folke/snacks.nvim/commit/effa885120670ca8a1775fc16ab2ec9e8040c288))

## [2.10.0](https://github.com/folke/snacks.nvim/compare/v2.9.0...v2.10.0) (2024-12-13)


### Features

* **animate:** add done to animation object ([ec73346](https://github.com/folke/snacks.nvim/commit/ec73346b7d4a25e440538141d5b8c68e42a1047d))
* **lazygit:** respect existing LG_CONFIG_FILE when setting config paths ([#208](https://github.com/folke/snacks.nvim/issues/208)) ([ef114c0](https://github.com/folke/snacks.nvim/commit/ef114c0efede3339221df5bc5b250aa9f8328b8d))
* **scroll:** added spamming detection and disable animations when user is spamming keys :) ([c58605f](https://github.com/folke/snacks.nvim/commit/c58605f8b3abf974e984ca5483cbe6ab9d2afc6e))
* **scroll:** improve smooth scrolling when user is spamming keys ([5532ba0](https://github.com/folke/snacks.nvim/commit/5532ba07be1306eb05c727a27368a8311bae3eeb))
* **zen:** added on_open / on_close callbacks ([5851de1](https://github.com/folke/snacks.nvim/commit/5851de157a08c96a0ca15580ced2ea53063fd65d))
* **zen:** make zen/zoom mode work for floating windows. Closes [#5028](https://github.com/folke/snacks.nvim/issues/5028) ([05bb957](https://github.com/folke/snacks.nvim/commit/05bb95739a362f8bec382f164f07c53137244627))


### Bug Fixes

* **dashboard:** set cursor to non-hidden actionable items. Fixes [#273](https://github.com/folke/snacks.nvim/issues/273) ([7c7b18f](https://github.com/folke/snacks.nvim/commit/7c7b18fdeeb7e228463b41d805cb47327f3d03f1))
* **indent:** fix rendering issues when `only_scope` is set for indent. Fixes [#268](https://github.com/folke/snacks.nvim/issues/268) ([370703d](https://github.com/folke/snacks.nvim/commit/370703da81a19db9d8bf41bb518e7b6959e53cea))
* **indent:** only render adjusted top/bottom. See [#268](https://github.com/folke/snacks.nvim/issues/268) ([54294cb](https://github.com/folke/snacks.nvim/commit/54294cba6a17ec048a63837fa341e6a663f3217d))
* **notifier:** set `modifiable=false` for notifier history ([12e68a3](https://github.com/folke/snacks.nvim/commit/12e68a33b5a1fd3648a7a558ef027fbb245125f7))
* **scope:** change from/to selection to make more sense ([e8dd394](https://github.com/folke/snacks.nvim/commit/e8dd394c01699276e8f7214957625222c30c8e9e))
* **scope:** possible loop? See [#278](https://github.com/folke/snacks.nvim/issues/278) ([ac6a748](https://github.com/folke/snacks.nvim/commit/ac6a74823b29cc1839df82fc839b81400ca80d45))
* **scratch:** normalize filename ([5200a8b](https://github.com/folke/snacks.nvim/commit/5200a8baa59a96e73786c11192282d2d3e10deeb))
* **scroll:** don't animate scroll distance 1 ([a986851](https://github.com/folke/snacks.nvim/commit/a986851a74512683c3331fa72a220751026fd611))

## [2.9.0](https://github.com/folke/snacks.nvim/compare/v2.8.0...v2.9.0) (2024-12-12)


### Features

* **animate:** allow disabling all animations globally or per buffer ([25c290d](https://github.com/folke/snacks.nvim/commit/25c290d7c093f0c57473ffcccf56780b6d58dd37))
* **animate:** allow toggling buffer-local / global animations with or without id ([50912dc](https://github.com/folke/snacks.nvim/commit/50912dc2fd926a49e3574d7029aed11fae3fb45b))
* **dashboard:** add dashboard startuptime icon option ([#214](https://github.com/folke/snacks.nvim/issues/214)) ([63506d5](https://github.com/folke/snacks.nvim/commit/63506d5168d2bb7679026cab80df1adfe3cd98b8))
* **indent:** animation styles `out`, `up_down`, `up`, `down` ([0a9b013](https://github.com/folke/snacks.nvim/commit/0a9b013ff13f6d1a550af2eac366c73d25e55e0a))
* **indent:** don't animate indents when new scope overlaps with the previous one, taking white-space into account. See [#264](https://github.com/folke/snacks.nvim/issues/264) ([9b4a859](https://github.com/folke/snacks.nvim/commit/9b4a85905aaa114e04a9371585953e96b3095f93))
* **indent:** move animate settings top-level, since they impact both scope and chunk ([baf8c18](https://github.com/folke/snacks.nvim/commit/baf8c180d9dda5797b4da538f7af122f4349f554))
* **toggle:** added zoom toggle ([3367705](https://github.com/folke/snacks.nvim/commit/336770581348c137bc2cb3967cc2af90b2ff51a2))
* **toggle:** return toggle after map ([4f22016](https://github.com/folke/snacks.nvim/commit/4f22016b4b765f3335ae7682fb5b3b79b414ecbd))
* **util:** get var either from buffer or global ([4243912](https://github.com/folke/snacks.nvim/commit/42439123c4fbc088fbe0bdd636a6bdc501794491))


### Bug Fixes

* **indent:** make sure cursor line is in scope for the `out` style. Fixes [#264](https://github.com/folke/snacks.nvim/issues/264) ([39c009f](https://github.com/folke/snacks.nvim/commit/39c009fe0b45243ccbbd372e659dcbd9409a68df))
* **indent:** when at edge of two blocks, prefer the one below. See [#231](https://github.com/folke/snacks.nvim/issues/231) ([2457d91](https://github.com/folke/snacks.nvim/commit/2457d913dc92835da12fc071b43bce3a03d31470))

## [2.8.0](https://github.com/folke/snacks.nvim/compare/v2.7.0...v2.8.0) (2024-12-11)


### Features

* **animate:** added animate plugin ([971229e](https://github.com/folke/snacks.nvim/commit/971229e8a93dab7dc73fe379110cdb47a7fd1387))
* **animate:** added animation context to callbacks ([1091280](https://github.com/folke/snacks.nvim/commit/109128087709fe5cba39e6983b8722b60cce8120))
* **dim:** added dim plugin ([4dda551](https://github.com/folke/snacks.nvim/commit/4dda5516e88a64c2b387727662d4ecd645582c55))
* **indent:** added indent plugin ([2c4021c](https://github.com/folke/snacks.nvim/commit/2c4021c4663ff4fe5da5b95c3e06a4f6eb416502))
* **indent:** allow disabling indent guides. See [#230](https://github.com/folke/snacks.nvim/issues/230) ([4a4ad63](https://github.com/folke/snacks.nvim/commit/4a4ad633dc9f864532716af4387b5e035d57768c))
* **indent:** allow disabling scope highlighting ([99207ee](https://github.com/folke/snacks.nvim/commit/99207ee44d3a2b4d14c915b08407bae708749235))
* **indent:** optional rendering of scopes as chunks. Closes [#230](https://github.com/folke/snacks.nvim/issues/230) ([109a0d2](https://github.com/folke/snacks.nvim/commit/109a0d207eeee49e13a6640131b44383e31d0b0f))
* **input:** added `input` snack ([70902ee](https://github.com/folke/snacks.nvim/commit/70902eee9e5aca7791450c6065dd51bed4651f24))
* **profiler:** on_close can now be a function ([48a5879](https://github.com/folke/snacks.nvim/commit/48a58792a0dd2e3c9249cfa4b1df73a8ea86a290))
* **scope:** added scope plugin ([63a279c](https://github.com/folke/snacks.nvim/commit/63a279c4e2e84ed02b5a2a6c2f84d68daf8f906a))
* **scope:** fill the range for treesitter scopes ([38ed01b](https://github.com/folke/snacks.nvim/commit/38ed01b5a229fc0c41b07dde08e9119de9ff1c4e))
* **scope:** text objects and jumping for scopes. Closes [#231](https://github.com/folke/snacks.nvim/issues/231) ([8faafb3](https://github.com/folke/snacks.nvim/commit/8faafb34831c48f3ff777d6bd0ced1c68c8ab82f))
* **scroll:** added smooth scrolling plugin ([38a5ccc](https://github.com/folke/snacks.nvim/commit/38a5ccc3a6436ba67fba71f6a2a9693ee1c2f142))
* **scroll:** allow disabling scroll globally or for some buffers ([04f15c1](https://github.com/folke/snacks.nvim/commit/04f15c1ba29afa6d1b085eb0d85a654c88be8fde))
* **scroll:** use `on_key` to track mouse scrolling ([26c3e49](https://github.com/folke/snacks.nvim/commit/26c3e4960f37320bcd418ec18f859b0e24d1e7d8))
* **scroll:** user virtual columns while scrolling ([fefa6fd](https://github.com/folke/snacks.nvim/commit/fefa6fd6920a2f8a6e717ae856c14e32d5d76ddb))
* **snacks:** zen mode ([c509ea5](https://github.com/folke/snacks.nvim/commit/c509ea52b7b3487e3d904d9f3d55d20ad136facb))
* **toggle:** add which-key mappings when which-key loads ([c9f494b](https://github.com/folke/snacks.nvim/commit/c9f494bd9a4729722186d2631ca91192ffc19b40))
* **toggle:** add zen mode toggle ([#243](https://github.com/folke/snacks.nvim/issues/243)) ([9454ba3](https://github.com/folke/snacks.nvim/commit/9454ba35f8c6ad3baeda4132fe1e5c96a5850960))
* **toggle:** added toggle for smooth scroll ([aeec09c](https://github.com/folke/snacks.nvim/commit/aeec09c5413c87df7ca827bd6b7c3fbf0f4d2909))
* **toggle:** toggles for new plugins ([bddae83](https://github.com/folke/snacks.nvim/commit/bddae83141d9e18b23a5d7a9ccc52c76ad736ca2))
* **util:** added Snacks.util.on_module to execute a callback when a module loads (or immediately when already loaded) ([f540b7b](https://github.com/folke/snacks.nvim/commit/f540b7b6cc10223adcbd6d9747155a076ddfa9a4))
* **util:** set_hl no longer sets default=true when not specified ([d6309c6](https://github.com/folke/snacks.nvim/commit/d6309c62b8e5910407449975b9e333c2699d06d0))
* **win:** added actions to easily combine actions in keymaps ([46362a5](https://github.com/folke/snacks.nvim/commit/46362a5a9c2583094bd0416dd6dea17996eaecf9))
* **win:** allow configuring initial text to display in the buffer ([003ea8d](https://github.com/folke/snacks.nvim/commit/003ea8d6edcf6d813bfdc143ffe4fa6cc55c0ea5))
* **win:** allow customizing backdrop window ([cdb495c](https://github.com/folke/snacks.nvim/commit/cdb495cb8f7b801d9d731cdfa2c6f92fadf1317d))
* **win:** col/row can be negative calculated on height/end of parent ([bd49d2f](https://github.com/folke/snacks.nvim/commit/bd49d2f32e567cbe42adf0bd8582b7829de6c1dc))
* **words:** added toggle for words ([bd7cf03](https://github.com/folke/snacks.nvim/commit/bd7cf038234a84b48d1c1f09dffae9e64910ff7e))
* **zen:** `zz` when entering zen mode ([b5cb90f](https://github.com/folke/snacks.nvim/commit/b5cb90f91dedaa692c4da1dfa216d13e58ad219d))
* **zen:** added zen plugin ([afb89ea](https://github.com/folke/snacks.nvim/commit/afb89ea159a20e1241656af5aa46f638327d2f5a))
* **zen:** added zoom indicator ([8459e2a](https://github.com/folke/snacks.nvim/commit/8459e2adc090aaf59865a60836c360744d82ed0a))


### Bug Fixes

* **compat:** fixes for Neovim &lt; 0.10 ([33fbb30](https://github.com/folke/snacks.nvim/commit/33fbb309f8c21c8ec30b99fe323a5cc55c84c5bc))
* **dashboard:** add filetype to terminal sections ([#215](https://github.com/folke/snacks.nvim/issues/215)) ([9c68a54](https://github.com/folke/snacks.nvim/commit/9c68a54af652ff69348848dad62c4cd901da59a0))
* **dashboard:** don't open with startup option args ([#222](https://github.com/folke/snacks.nvim/issues/222)) ([6b78172](https://github.com/folke/snacks.nvim/commit/6b78172864ef94cd5f2ab184c0f98cf36f5a8e74))
* **dashboard:** override foldmethod ([47ad2a7](https://github.com/folke/snacks.nvim/commit/47ad2a7bfa49c3eb5c20083de82a39f59fb8f17a))
* **debug:** schedule wrap print ([3a107af](https://github.com/folke/snacks.nvim/commit/3a107afbf8dffabf6c2754750c51d740707b76af))
* **dim:** check if win still exist when animating. Closes [#259](https://github.com/folke/snacks.nvim/issues/259) ([69018d0](https://github.com/folke/snacks.nvim/commit/69018d070c9a0db76abc0f34539287e1c181a5d4))
* **health:** health checks ([72eba84](https://github.com/folke/snacks.nvim/commit/72eba841801928b00a1f1f74e1f976a31534a674))
* **indent:** always align indents with shiftwidth ([1de6c15](https://github.com/folke/snacks.nvim/commit/1de6c152883b576524201a465e1b8a09622a6041))
* **indent:** always render underline regardless of leftcol ([4e96e69](https://github.com/folke/snacks.nvim/commit/4e96e692e8a3c6c67f9c9b2971b6fa263461054b))
* **indent:** always use scope hl to render underlines. Fixes [#234](https://github.com/folke/snacks.nvim/issues/234) ([8723945](https://github.com/folke/snacks.nvim/commit/8723945183aac31671d1fa27481c90dc7c665c02))
* **indent:** better way of dealing with indents on blank lines. See [#246](https://github.com/folke/snacks.nvim/issues/246) ([c129683](https://github.com/folke/snacks.nvim/commit/c1296836f5c36e75b508aae9a76aa0931058feec))
* **indent:** expand scopes to inlude end_pos based on the end_pos scope. See [#231](https://github.com/folke/snacks.nvim/issues/231) ([897f801](https://github.com/folke/snacks.nvim/commit/897f8019248009eca191185cf7094e04c9371a85))
* **indent:** gradually increase scope when identical to visual selection for text objects ([bc7f96b](https://github.com/folke/snacks.nvim/commit/bc7f96bdee77368d4ddae2613823f085266529ab))
* **indent:** properly deal with empty lines when highlighting scopes. Fixes [#246](https://github.com/folke/snacks.nvim/issues/246). Fixes [#245](https://github.com/folke/snacks.nvim/issues/245) ([d04cf1d](https://github.com/folke/snacks.nvim/commit/d04cf1dc4f332bade99c300283380bf0893f1996))
* **indent:** set max_size=1 for textobjects and jumps by default. See [#231](https://github.com/folke/snacks.nvim/issues/231) ([5f217bc](https://github.com/folke/snacks.nvim/commit/5f217bca6adc88a5dc6aa55e5bf0580b95025a52))
* **indent:** set shiftwidth to tabstop when 0 ([782b6ee](https://github.com/folke/snacks.nvim/commit/782b6ee3fca35ede62b8dba866a9ad5c50edfdce))
* **indent:** underline. See [#234](https://github.com/folke/snacks.nvim/issues/234) ([51f9569](https://github.com/folke/snacks.nvim/commit/51f95693aedcde10e65c8121c6fd1293a3ac3819))
* **indent:** use correct config options ([5352198](https://github.com/folke/snacks.nvim/commit/5352198b5a59968c871b962fa15f1d7ca4eb7b52))
* **init:** enabled check ([519a45b](https://github.com/folke/snacks.nvim/commit/519a45bfe5df7fdf5aea0323e978e20eb52e15bc))
* **init:** set input disabled by default. Fixes [#227](https://github.com/folke/snacks.nvim/issues/227) ([e9d0993](https://github.com/folke/snacks.nvim/commit/e9d099322fca1bb7b35e781230e4a7478ded86bf))
* **input:** health check. Fixes [#239](https://github.com/folke/snacks.nvim/issues/239) ([acf743f](https://github.com/folke/snacks.nvim/commit/acf743fcfc4e0e42e1c9fe5c06f677849fa38e8b))
* **input:** set current win before executing callback. Fixes [#257](https://github.com/folke/snacks.nvim/issues/257) ([c17c1b2](https://github.com/folke/snacks.nvim/commit/c17c1b2f6c99f0ed4f3ab8f00bf32bb39f9d0186))
* **input:** set current win in `vim.schedule` so that it works properly from `expr` keymaps. Fixes [#257](https://github.com/folke/snacks.nvim/issues/257) ([8c2410c](https://github.com/folke/snacks.nvim/commit/8c2410c2de0a86c095177a831993f8eea78a63b6))
* **input:** update window position in the context of the parent window to make sure position=cursor works as expected. Fixes [#254](https://github.com/folke/snacks.nvim/issues/254) ([6c27ff2](https://github.com/folke/snacks.nvim/commit/6c27ff2a365c961831c7620365dd87fa8d8ad633))
* **input:** various minor visual fixes ([#252](https://github.com/folke/snacks.nvim/issues/252)) ([e01668c](https://github.com/folke/snacks.nvim/commit/e01668c36771c0c1424a2ce3ab26a09cbb43d472))
* **notifier:** toggle show history. Fixes [#197](https://github.com/folke/snacks.nvim/issues/197) ([8b58b55](https://github.com/folke/snacks.nvim/commit/8b58b55e40221ca5124f156f47e46185310fbe1c))
* **scope:** better edge detection for treesitter scopes ([6b02a09](https://github.com/folke/snacks.nvim/commit/6b02a09e5e81e4e38a42e0fcc2d7f0350404c228))
* **scope:** return `nil` when buffer is empty for indent scope ([4aa378a](https://github.com/folke/snacks.nvim/commit/4aa378a35e8f3d3771410344525cc4bc9ac50e8a))
* **scope:** take edges into account for min_size ([e2e6c86](https://github.com/folke/snacks.nvim/commit/e2e6c86d214029bfeae5d50929aee72f7059b7b7))
* **scope:** typo for textobject ([0324125](https://github.com/folke/snacks.nvim/commit/0324125ca1e5a5e6810d28ea81bb2c7c0af1dc16))
* **scroll:** better toggle ([3dcaad8](https://github.com/folke/snacks.nvim/commit/3dcaad8d0aacc1a736f75fee5719adbf80cbbfa2))
* **scroll:** disable scroll by default for terminals ([7b5a78a](https://github.com/folke/snacks.nvim/commit/7b5a78a5c76cdf2c73abbeef47ac14bb8ccbee72))
* **scroll:** don't animate invalid windows ([41ca13d](https://github.com/folke/snacks.nvim/commit/41ca13d119b328872ed1da9c8f458c5c24962d31))
* **scroll:** don't bother setting cursor when scrolloff is larger than half of viewport. Fixes [#240](https://github.com/folke/snacks.nvim/issues/240) ([0ca9ca7](https://github.com/folke/snacks.nvim/commit/0ca9ca79926b3bf473172eee7072fa2838509fba))
* **scroll:** move scrollbind check to M.check ([7211ec0](https://github.com/folke/snacks.nvim/commit/7211ec08ce01da754544f66e965effb13fd22fd3))
* **scroll:** only animate the current window when scrollbind is active ([c9880ce](https://github.com/folke/snacks.nvim/commit/c9880ce872ca000d17ae8d62b10e913045f54735))
* **scroll:** set cursor to correct position when target is reached. Fixes [#236](https://github.com/folke/snacks.nvim/issues/236) ([4209929](https://github.com/folke/snacks.nvim/commit/4209929e6d4f67d3d216d1ab5f52dc387c8e27c2))
* **scroll:** use actual scrolling to perform the scroll to properly deal with folds etc. Fixes [#236](https://github.com/folke/snacks.nvim/issues/236) ([280a09e](https://github.com/folke/snacks.nvim/commit/280a09e4eef08157eface8398295eb7fa3f9a08d))
* **win:** ensure win is set when relative=win ([5d472b8](https://github.com/folke/snacks.nvim/commit/5d472b833b7f925033fea164de0ab9e389e31bef))
* **words:** incorrect enabled check. Fixes [#247](https://github.com/folke/snacks.nvim/issues/247) ([9c8f3d5](https://github.com/folke/snacks.nvim/commit/9c8f3d531874ebd20eebe259f5c30cea575a1bba))
* **zen:** properly close existing zen window on toggle ([14da56e](https://github.com/folke/snacks.nvim/commit/14da56ee9791143ef2503816fb93f8bd2bf0b58d))
* **zen:** return after closing. Fixes [#259](https://github.com/folke/snacks.nvim/issues/259) ([b13eaf6](https://github.com/folke/snacks.nvim/commit/b13eaf6bd9089d8832c79f4088e72affc449c8ee))
* **zen:** when Normal is transparent, show an opaque transparent backdrop. Fixes [#235](https://github.com/folke/snacks.nvim/issues/235) ([d93de7a](https://github.com/folke/snacks.nvim/commit/d93de7af6916d2c734c9420624b8703236f386ff))


### Performance Improvements

* **animate:** check for animation easing updates ouside the main loop and schedule an update when needed ([03c0774](https://github.com/folke/snacks.nvim/commit/03c0774e8555a38267624309d4f00a30e351c4be))
* **input:** lazy-load `vim.ui.input` ([614df63](https://github.com/folke/snacks.nvim/commit/614df63acfb5ce9b1ac174ea4f09e545a086af4d))
* **util:** redraw helpers ([9fb88c6](https://github.com/folke/snacks.nvim/commit/9fb88c67b60cbd9d4a56f9aadcb9285929118518))

## [2.7.0](https://github.com/folke/snacks.nvim/compare/v2.6.0...v2.7.0) (2024-12-07)


### Features

* **bigfile:** disable matchparen, set foldmethod=manual, set conceallevel=0 ([891648a](https://github.com/folke/snacks.nvim/commit/891648a483b6f5410ec9c8b74890d5a00b50fa4c))
* **dashbard:** explude files from stdpath data/cache/state in recent files and projects ([b99bc64](https://github.com/folke/snacks.nvim/commit/b99bc64ef910cd075e4ab9cf0914e99e1a1d61c1))
* **dashboard:** allow items to be hidden, but still create the keymaps etc ([7a47eb7](https://github.com/folke/snacks.nvim/commit/7a47eb76df2fd36bfcf3ed5c4da871542e1386be))
* **dashboard:** allow terminal sections to have actions. Closes [#189](https://github.com/folke/snacks.nvim/issues/189) ([78f44f7](https://github.com/folke/snacks.nvim/commit/78f44f720b1b0609930581965f4f92649efae95b))
* **dashboard:** hide title if section has no items. Fixes [#184](https://github.com/folke/snacks.nvim/issues/184) ([d370be6](https://github.com/folke/snacks.nvim/commit/d370be6d6966a298725901abad4bec90264859af))
* **dashboard:** make buffer not listed ([#191](https://github.com/folke/snacks.nvim/issues/191)) ([42d6277](https://github.com/folke/snacks.nvim/commit/42d62775d82b7af4dbe001b04be6a8a6e461e8ec))
* **dashboard:** when a section has a title, use that for action and key. If not put it in the section. Fixes [#189](https://github.com/folke/snacks.nvim/issues/189) ([045a17d](https://github.com/folke/snacks.nvim/commit/045a17da069aae6221b2b3eae8610c2aa5ca03ea))
* **debug:** added `Snacks.debug.run()` to execute the buffer or selection with inlined print and errors ([e1fe4f5](https://github.com/folke/snacks.nvim/commit/e1fe4f5afed5c679fd2eed3486f17e8c0994b982))
* **gitbrowse:** added `line_count`. See [#186](https://github.com/folke/snacks.nvim/issues/186) ([f03727c](https://github.com/folke/snacks.nvim/commit/f03727c77f739503fd297dd12a826c2aca3490f9))
* **gitbrowse:** opts.notify ([a856952](https://github.com/folke/snacks.nvim/commit/a856952ab24757f4eaf4ae2e1728d097f1866681))
* **gitbrowse:** url pattern can now also be a function ([0a48c2e](https://github.com/folke/snacks.nvim/commit/0a48c2e726e6ca90370260a05618f45345dbb66a))
* **notifier:** reverse notif history by default for `show_history` ([5a50738](https://github.com/folke/snacks.nvim/commit/5a50738b8e952519658570f31ff5f24e06882f18))
* **scratch:** `opts.ft` can now be a function and defaults to the ft of the current buffer or markdown ([652303e](https://github.com/folke/snacks.nvim/commit/652303e6de5fa746709979425f4bed763e3fcbfa))
* **scratch:** change keymap to execute buffer/selection to `&lt;cr&gt;` ([7db0ed4](https://github.com/folke/snacks.nvim/commit/7db0ed4239a2f67c0ca288aaac21bc6aa65212a7))
* **scratch:** use `Snacks.debug.run()` to execute buffer/selection ([32c46b4](https://github.com/folke/snacks.nvim/commit/32c46b4e2f61c026e41b3fa128d01b0e89da106c))
* **snacks:** added `Snacks.profiler` ([8088799](https://github.com/folke/snacks.nvim/commit/808879951f960399844c89efef9aec1724f83402))
* **snacks:** added new `scratch` snack ([1cec695](https://github.com/folke/snacks.nvim/commit/1cec695fefb6e42ee644cfaf282612c213009aed))
* **toggle:** toggles for the profiler ([999ae07](https://github.com/folke/snacks.nvim/commit/999ae07808858df08d30eb099a8dbce401527008))
* **util:** encode/decode a string to be used as a filename ([e6f6397](https://github.com/folke/snacks.nvim/commit/e6f63970de2225ad44ed08af7ffd8a0f37d8fc58))
* **util:** simple function to get an icon ([7c29848](https://github.com/folke/snacks.nvim/commit/7c29848e89861b40e751cc15c557cf1e574acf66))
* **win:** added `opts.fixbuf` to configure fixed window buffers ([1f74d1c](https://github.com/folke/snacks.nvim/commit/1f74d1ce77d2015e2802027c93b9e0bcd548e4d1))
* **win:** backdrop can now be made opaque ([681b9c9](https://github.com/folke/snacks.nvim/commit/681b9c9d650e7b01a5e54567656f646fbd3b8d46))
* **win:** width/height can now be a function ([964d7ae](https://github.com/folke/snacks.nvim/commit/964d7ae99af1f45949175e1494562a796e2ef99b))


### Bug Fixes

* **dashboard:** calculate proper offset when item has no text ([6e3b954](https://github.com/folke/snacks.nvim/commit/6e3b9546de4871a696652cfcee6768c39e7b8ee9))
* **dashboard:** prevent possible duplicate recent files. Fixes [#171](https://github.com/folke/snacks.nvim/issues/171) ([93b254d](https://github.com/folke/snacks.nvim/commit/93b254d65845aa44ad1e01000c26e1faf5efb9a6))
* **dashboard:** take hidden items into account when calculating padding. Fixes [#194](https://github.com/folke/snacks.nvim/issues/194) ([736ce44](https://github.com/folke/snacks.nvim/commit/736ce447e8815eb231271abf7d49d8fa7d96e225))
* **dashboard:** take indent into account when calculating terminal width ([cda695e](https://github.com/folke/snacks.nvim/commit/cda695e53ffb34c7569dc3536134c9e432b2a1c1))
* **dashboard:** truncate file names when too long. Fixes [#183](https://github.com/folke/snacks.nvim/issues/183) ([4bdf7da](https://github.com/folke/snacks.nvim/commit/4bdf7daece384ab8a5472e6effd5fd6167a5ce6a))
* **debug:** better way of getting visual selection. See [#190](https://github.com/folke/snacks.nvim/issues/190) ([af41cb0](https://github.com/folke/snacks.nvim/commit/af41cb088d3ff8da9dede9fbdd5c81860bc37e64))
* **gitbrowse:** opts.notify ([2436557](https://github.com/folke/snacks.nvim/commit/243655796e4adddf58ce581f1b86a283130ecf41))
* **gitbrowse:** removed debug ([f894952](https://github.com/folke/snacks.nvim/commit/f8949523ed3f27f976e5346051a6658957d9492a))
* **gitbrowse:** use line_start and line_end directly in patterns. Closes [#186](https://github.com/folke/snacks.nvim/issues/186) ([adf0433](https://github.com/folke/snacks.nvim/commit/adf0433e8fca3fbc7287ac7b05f01f10ac354283))
* **profiler:** startup opts ([85f5132](https://github.com/folke/snacks.nvim/commit/85f51320b2662830a6435563668a04ab21686178))
* **scratch:** always set filetype on the buffer. Fixes [#179](https://github.com/folke/snacks.nvim/issues/179) ([6db50cf](https://github.com/folke/snacks.nvim/commit/6db50cfe2d1b333512e8175d072a2f82e796433d))
* **scratch:** floating window title/footer hl groups ([6c25ab1](https://github.com/folke/snacks.nvim/commit/6c25ab1108d12ef3642e97e3757710e69782cbd1))
* **scratch:** make sure win.opts.keys exists. See [#190](https://github.com/folke/snacks.nvim/issues/190) ([50bd510](https://github.com/folke/snacks.nvim/commit/50bd5103ba0a15294b1a931fda14900e7fd10161))
* **scratch:** sort keys. Fixes [#193](https://github.com/folke/snacks.nvim/issues/193) ([0df7a08](https://github.com/folke/snacks.nvim/commit/0df7a08b01b037e434efe7cd25e7d4608a282a92))
* **scratch:** weirdness with visual selection and inclusive/exclusive. See [#190](https://github.com/folke/snacks.nvim/issues/190) ([f955f08](https://github.com/folke/snacks.nvim/commit/f955f082e09683d02df3fd0f8b621b938f38e6aa))
* **statuscolumn:** add virtnum and relnum to cache key. See [#198](https://github.com/folke/snacks.nvim/issues/198) ([3647ca7](https://github.com/folke/snacks.nvim/commit/3647ca7d8a47362a01b539f5b6efc2d5339b5e8e))
* **statuscolumn:** don't show signs on virtual ligns. See [#198](https://github.com/folke/snacks.nvim/issues/198) ([f5fb59c](https://github.com/folke/snacks.nvim/commit/f5fb59cc4c62e25745bbefd2ef55665a67196245))
* **util:** better support for nvim-web-devicons ([ddaa2aa](https://github.com/folke/snacks.nvim/commit/ddaa2aaba59bbd05c03992bfb295c98ccd3b3e50))
* **util:** make sure to always return an icon ([ca7188c](https://github.com/folke/snacks.nvim/commit/ca7188c531350fe313c211ad60a59d642749f93e))
* **win:** allow resolving nil window option values. Fixes [#179](https://github.com/folke/snacks.nvim/issues/179) ([0043fa9](https://github.com/folke/snacks.nvim/commit/0043fa9ee142b63ab653507f2e6f45395e8a23d5))
* **win:** don't force close modified buffers ([d517b11](https://github.com/folke/snacks.nvim/commit/d517b11cabf94bf833d020c7a0781122d0f48c06))
* **win:** update opts.wo for padding instead of vim.wo directly ([446f502](https://github.com/folke/snacks.nvim/commit/446f50208fe823787ce60a8b216a622a4b6b63dd))
* **win:** update window local options when the buffer changes ([630d96c](https://github.com/folke/snacks.nvim/commit/630d96cf1f0403352580f2d119fc3b3ba29e33a4))


### Performance Improvements

* **dashboard:** properly cleanup autocmds ([8e6d977](https://github.com/folke/snacks.nvim/commit/8e6d977ec985a1b3f12a53741df82881a7835f9a))
* **statuscolumn:** optimize caching ([d972bc0](https://github.com/folke/snacks.nvim/commit/d972bc0a471fbf3067a115924a4add852d15f5f0))

## [2.6.0](https://github.com/folke/snacks.nvim/compare/v2.5.0...v2.6.0) (2024-11-29)


### Features

* **config:** allow overriding resolved options for a plugin. See [#164](https://github.com/folke/snacks.nvim/issues/164) ([d730a13](https://github.com/folke/snacks.nvim/commit/d730a13b5519fa901114cbdd0b2d484067068cce))
* **config:** make it easier to use examples in your config ([6e3cb7e](https://github.com/folke/snacks.nvim/commit/6e3cb7e53c0a1b314203d392dc1b7df8207a31a6))
* **dashboard:** allow passing win=0, buf=0 to use for the dashboard instead of creating a new window ([417e07c](https://github.com/folke/snacks.nvim/commit/417e07c0d22173a0a50ed8207e913ba25b96088e))
* **dashboard:** always render cache even when expired. Then refresh when needed. ([59f8f0d](https://github.com/folke/snacks.nvim/commit/59f8f0db99e7a2d4f6a181f02f3fe77355c016c8))
* **gitbrowse:** add Bitbucket URL patterns ([#163](https://github.com/folke/snacks.nvim/issues/163)) ([53441c9](https://github.com/folke/snacks.nvim/commit/53441c97030dbc15b4a22d56e33054749a13750f))
* **gitbrowse:** open commit when word is valid hash ([#161](https://github.com/folke/snacks.nvim/issues/161)) ([59c8eb3](https://github.com/folke/snacks.nvim/commit/59c8eb36ae6933dd54d87811fec22decfe4f303c))
* **health:** check that snacks.nvim plugin spec is correctly setup ([2c7b4b7](https://github.com/folke/snacks.nvim/commit/2c7b4b7971c8b488cfc9949f402f6c0307e24fce))
* **notifier:** added history opts.reverse ([bebd7e7](https://github.com/folke/snacks.nvim/commit/bebd7e70cdd336dcc582227c2f3bd6ea0cef60d9))
* **win:** go back to the previous window, when closing a snacks window ([51996df](https://github.com/folke/snacks.nvim/commit/51996dfeac5f0936aa1196e90b28760eb028ac1a))


### Bug Fixes

* **config:** check correct var for single config result. Fixes [#167](https://github.com/folke/snacks.nvim/issues/167) ([45fd0ef](https://github.com/folke/snacks.nvim/commit/45fd0efe41a453f1fa54a0892d352b931d6f88bb))
* **dashboard:** fixed mini.sessions.read. Fixes [#144](https://github.com/folke/snacks.nvim/issues/144) ([4e04b70](https://github.com/folke/snacks.nvim/commit/4e04b70ea3f6f91ae47e0fc7671e53e801171290))
* **dashboard:** terminal commands get 5 seconds to complete to trigger caching ([f83a7b0](https://github.com/folke/snacks.nvim/commit/f83a7b0ffb13adfae55a464f4d99fe3d4b578fe6))
* **git:** make git.get_root work for work-trees and cache git root checks. Closes [#136](https://github.com/folke/snacks.nvim/issues/136). Fixes [#115](https://github.com/folke/snacks.nvim/issues/115) ([9462273](https://github.com/folke/snacks.nvim/commit/9462273bf7c0e627da0f412c02daee907947078d))
* **init:** use rawget when loading modules to prevent possible recursive loading with invalid module fields ([d0794dc](https://github.com/folke/snacks.nvim/commit/d0794dcf8e988cf70c8db705a6e65867ba3b6e30))
* **notifier:** always show notifs directly when blocking ([0c7f7c5](https://github.com/folke/snacks.nvim/commit/0c7f7c5970d204d62488a4e351f1f1514a2a42e5))
* **notifier:** gracefully handle E565 errors ([0bbc9e7](https://github.com/folke/snacks.nvim/commit/0bbc9e7ae65820bc5ee356e1321656a7106d409a))
* **statuscolumn:** bad copy/paste!! Fixes [#152](https://github.com/folke/snacks.nvim/issues/152) ([7564a30](https://github.com/folke/snacks.nvim/commit/7564a30cad803c01f8ecc15683a280d2f0e9bdb7))
* **statuscolumn:** never error (in case of E565 for example). Fixes [#150](https://github.com/folke/snacks.nvim/issues/150) ([cf84008](https://github.com/folke/snacks.nvim/commit/cf840087c5adf1c076b61fdd044ac960b31e4e1e))
* **win:** handle E565 errors on close ([0b02044](https://github.com/folke/snacks.nvim/commit/0b020449ad8496c6bfd34e10bc69f807b52970f8))


### Performance Improvements

* **statuscolumn:** some small optims ([985be4a](https://github.com/folke/snacks.nvim/commit/985be4a759f6fe83e569679da431eeb7d2db5286))

## [2.5.0](https://github.com/folke/snacks.nvim/compare/v2.4.0...v2.5.0) (2024-11-22)


### Features

* **dashboard:** added Snacks.dashboard.update(). Closes [#121](https://github.com/folke/snacks.nvim/issues/121) ([c770ebe](https://github.com/folke/snacks.nvim/commit/c770ebeaf7b19abad8a447ef55b48cec71e7db54))
* **debug:** profile title ([0177079](https://github.com/folke/snacks.nvim/commit/017707955f465335900c4fd483c32df018fd3427))
* **notifier:** show indicator when notif has more lines. Closes [#112](https://github.com/folke/snacks.nvim/issues/112) ([cf72c06](https://github.com/folke/snacks.nvim/commit/cf72c06ee61f3102bf828ee7e8dde20316310374))
* **terminal:** added Snacks.terminal.get(). Closes [#122](https://github.com/folke/snacks.nvim/issues/122) ([7f63d4f](https://github.com/folke/snacks.nvim/commit/7f63d4fefb7ba22f6e98986f7adeb04f9f9369b1))
* **util:** get hl color ([b0da066](https://github.com/folke/snacks.nvim/commit/b0da066536493b6ed977744e4ee91fac01fcc2a8))
* **util:** set_hl managed ([9642695](https://github.com/folke/snacks.nvim/commit/96426953a029b12d02ad45849e086c1ee14e065b))


### Bug Fixes

* **dashboard:** `vim.pesc` for auto keys. Fixes [#134](https://github.com/folke/snacks.nvim/issues/134) ([aebffe5](https://github.com/folke/snacks.nvim/commit/aebffe535b09237b28d2c61bb3febab12bc95ae8))
* **dashboard:** align should always set width even if no alignment is needed. Fixes [#137](https://github.com/folke/snacks.nvim/issues/137) ([54d521c](https://github.com/folke/snacks.nvim/commit/54d521cd0fde5e3ccf36716f23371707d0267768))
* **dashboard:** better git check for advanced example. See [#126](https://github.com/folke/snacks.nvim/issues/126) ([b4a293a](https://github.com/folke/snacks.nvim/commit/b4a293aac747fbde7aafa72242a2d26dc17e325d))
* **dashboard:** open fullscreen on relaunch ([853240b](https://github.com/folke/snacks.nvim/commit/853240bb207ed7a2366c6c63ffc38f3b26fd484f))
* **dashboard:** randomseed needs argument on stable ([c359164](https://github.com/folke/snacks.nvim/commit/c359164872e82646e11c652fb0fbe723e58bfdd8))
* **debug:** include `main` in caller ([33d31af](https://github.com/folke/snacks.nvim/commit/33d31af1501ec154dba6008064d17ab72ec37d00))
* **git:** get_root should work for non file buffers ([723d8ea](https://github.com/folke/snacks.nvim/commit/723d8eac849749e9015d9e9598f99974684ca3bb))
* **notifier:** hide existing nofif if higher prio notif arrives and no free space for lower notif ([7a061de](https://github.com/folke/snacks.nvim/commit/7a061de75f758db23cf2c2ee0822a76356b54035))
* **quickfile:** don't load when bigfile detected. Fixes [#116](https://github.com/folke/snacks.nvim/issues/116) ([978424c](https://github.com/folke/snacks.nvim/commit/978424ce280ec85e78e9660b200aee9aa12e9ef2))
* **sessions:** change persisted.nvim load session command ([#118](https://github.com/folke/snacks.nvim/issues/118)) ([26bec4b](https://github.com/folke/snacks.nvim/commit/26bec4b51d617cd275218e9935fdff5390c18a87))
* **terminal:** hide on `q` instead of close ([30a0721](https://github.com/folke/snacks.nvim/commit/30a0721d56993a7125a247a07116f1a07e0efda4))

## [2.4.0](https://github.com/folke/snacks.nvim/compare/v2.3.0...v2.4.0) (2024-11-19)


### Features

* **dashboard:** hide tabline and statusline when loading during startup ([75dc74c](https://github.com/folke/snacks.nvim/commit/75dc74c5dc933b81cde85e8bc368a384343af69f))
* **dashboard:** when an item is wider than pane width and only one pane, then center it. See [#108](https://github.com/folke/snacks.nvim/issues/108) ([c15953e](https://github.com/folke/snacks.nvim/commit/c15953ee885cf8afb40dcd478569d7de2edae939))
* **gitbrowse:** open also visual selection range ([#89](https://github.com/folke/snacks.nvim/issues/89)) ([c29c0d4](https://github.com/folke/snacks.nvim/commit/c29c0d48500cb976c9210bb2d42909ad203cd4aa))
* **win:** detect alien buffers opening in managed windows and open them somewhere else. Fixes [#110](https://github.com/folke/snacks.nvim/issues/110) ([9c0d2e2](https://github.com/folke/snacks.nvim/commit/9c0d2e2e93615e70627e5c09c7bbb04e93eab2c6))


### Bug Fixes

* **dashboard:** always hide cursor ([68fcc25](https://github.com/folke/snacks.nvim/commit/68fcc258023404a0a0341a7cc93db47cd17f85f4))
* **dashboard:** check session managers in order ([1acea8b](https://github.com/folke/snacks.nvim/commit/1acea8b94005620dad70dfde6a6344c130a57c59))
* **dashboard:** fix race condition when sending data while closing ([4188446](https://github.com/folke/snacks.nvim/commit/4188446f86b5c6abae090eb6abca65d5d9bb8003))
* **dashboard:** minimum one pane even when it doesn't fit the screen. Fixes [#104](https://github.com/folke/snacks.nvim/issues/104) ([be8feef](https://github.com/folke/snacks.nvim/commit/be8feef4ab584f50aaa96b69d50b3f86a35aacff))
* **dashboard:** only check for piped stdin when in TUI. Ignore GUIs ([3311d75](https://github.com/folke/snacks.nvim/commit/3311d75f893191772a1b9525b18b94d8c3a8943a))
* **dashboard:** remove weird preset.keys function override. Just copy defaults if you want to change them ([0b9e09c](https://github.com/folke/snacks.nvim/commit/0b9e09cbd97c178ebe5db78fd373448448c5511b))

## [2.3.0](https://github.com/folke/snacks.nvim/compare/v2.2.0...v2.3.0) (2024-11-18)


### Features

* added dashboard health checks ([deb00d0](https://github.com/folke/snacks.nvim/commit/deb00d0ddc57d77f5f6c3e5510ba7c2f07e593eb))
* **dashboard:** added support for mini.sessions ([c8e209e](https://github.com/folke/snacks.nvim/commit/c8e209e6be9e8d8cdee19842a99ae7b89ac4248d))
* **dashboard:** allow opts.preset.keys to be a function with default keymaps as arg ([b7775ec](https://github.com/folke/snacks.nvim/commit/b7775ec879e14362d8e4082b7ed97a752bbb654a))
* **dashboard:** automatically detect streaming commands and don't cache those. tty-clock, cmatrix galore. Fixes [#100](https://github.com/folke/snacks.nvim/issues/100) ([082beb5](https://github.com/folke/snacks.nvim/commit/082beb508ccf3584aebfee845c1271d9f8b8abb6))
* **notifier:** timeout=0 keeps the notif visible till manually hidden. Closes [#102](https://github.com/folke/snacks.nvim/issues/102) ([0cf22a8](https://github.com/folke/snacks.nvim/commit/0cf22a8d87f28759c083969d67b55498e568a1b7))


### Bug Fixes

* **dashboard:** check uis for headless and stdin_tty. Fixes [#97](https://github.com/folke/snacks.nvim/issues/97). Fixes [#98](https://github.com/folke/snacks.nvim/issues/98) ([4ff08f1](https://github.com/folke/snacks.nvim/commit/4ff08f1c4d7b7b5d3e1da3b2cc9d6c341cd4dc1a))
* **dashboard:** debug output ([c0129da](https://github.com/folke/snacks.nvim/commit/c0129da4f839fd4306627b087cb722ea54c50c18))
* **dashboard:** disable `vim.wo.colorcolumn` ([#101](https://github.com/folke/snacks.nvim/issues/101)) ([43b4abb](https://github.com/folke/snacks.nvim/commit/43b4abb9f11a07d7130b461f9bd96b3e4e3c5b94))
* **dashboard:** notify on errors. Fixes [#99](https://github.com/folke/snacks.nvim/issues/99) ([2ae4108](https://github.com/folke/snacks.nvim/commit/2ae410889cbe6f59fb52f40cb86b25d6f7e874e2))
* **debug:** MYVIMRC is not always set ([735f4d8](https://github.com/folke/snacks.nvim/commit/735f4d8c9de6fcff31bce671495569f345818ea0))
* **notifier:** also handle timeout = false / timeout = true. See [#102](https://github.com/folke/snacks.nvim/issues/102) ([99f1f49](https://github.com/folke/snacks.nvim/commit/99f1f49104d413dabee9ee45bc22366aee97056e))

## [2.2.0](https://github.com/folke/snacks.nvim/compare/v2.1.0...v2.2.0) (2024-11-18)


### Features

* **dashboard:** added new `dashboard` snack ([#77](https://github.com/folke/snacks.nvim/issues/77)) ([d540fa6](https://github.com/folke/snacks.nvim/commit/d540fa607c415b55f5a0d773f561c19cd6287de4))
* **debug:** Snacks.debug.trace and Snacks.debug.stats for hierarchical traces (like lazy profile) ([b593598](https://github.com/folke/snacks.nvim/commit/b593598859b1bb3946671fc78ee1896d32460552))
* **notifier:** global keep when in cmdline ([73b1e20](https://github.com/folke/snacks.nvim/commit/73b1e20d38d4d238316ed391faf3d7ad4c3e71be))

## [2.1.0](https://github.com/folke/snacks.nvim/compare/v2.0.0...v2.1.0) (2024-11-16)


### Features

* **notifier:** allow specifying a minimal level to show notifications. See [#82](https://github.com/folke/snacks.nvim/issues/82) ([ec9cfb3](https://github.com/folke/snacks.nvim/commit/ec9cfb36b1ea2b4bf21b5812791c8bfee3bcf322))
* **terminal:** when terminal terminates too quickly, don't close the window and show an error message. See [#80](https://github.com/folke/snacks.nvim/issues/80) ([313954e](https://github.com/folke/snacks.nvim/commit/313954efdfb064a85df731b29fa9b86bc711044a))


### Bug Fixes

* **docs:** typo in README.md ([#78](https://github.com/folke/snacks.nvim/issues/78)) ([dc0f404](https://github.com/folke/snacks.nvim/commit/dc0f4041dcc8da860bdf84c3bf27d41a6a4debf3))
* **example:** rename file. Closes [#76](https://github.com/folke/snacks.nvim/issues/76) ([00c7a67](https://github.com/folke/snacks.nvim/commit/00c7a674004665999fbea310a322f1e105e1cfb5))
* **notifier:** no gap in border without title/icon ([#85](https://github.com/folke/snacks.nvim/issues/85)) ([bc80bdc](https://github.com/folke/snacks.nvim/commit/bc80bdcc62efd236617dbbd183ce5882aded2145))
* **win:** delay when closing windows ([#81](https://github.com/folke/snacks.nvim/issues/81)) ([d3dc8e7](https://github.com/folke/snacks.nvim/commit/d3dc8e7c27a663e4b30579e4e1ca3313052d0874))

## [2.0.0](https://github.com/folke/snacks.nvim/compare/v1.2.0...v2.0.0) (2024-11-14)


### ⚠ BREAKING CHANGES

* **config:** plugins are no longer enabled by default. Pass any options, or set `enabled = true`.

### Features

* **config:** plugins are no longer enabled by default. Pass any options, or set `enabled = true`. ([797708b](https://github.com/folke/snacks.nvim/commit/797708b0384ddfd66118651c48c3b399e376cb77))
* **health:** added health checks to plugins ([1c4c748](https://github.com/folke/snacks.nvim/commit/1c4c74828fcca382f54817f4446649b201d56557))
* **terminal:** added `Snacks.terminal.colorize()` to replace ansi codes by colors ([519b684](https://github.com/folke/snacks.nvim/commit/519b6841c42c575aec2ffc6c79c4e0a1a13e74bd))


### Bug Fixes

* **lazygit:** not needed to use deprecated fallback for set_hl ([14f076e](https://github.com/folke/snacks.nvim/commit/14f076e039aa876ba086449a45053d847bddb3db))
* **notifier:** disable `colorcolumn` by default ([#66](https://github.com/folke/snacks.nvim/issues/66)) ([7627b81](https://github.com/folke/snacks.nvim/commit/7627b81d9f3453bd2e979d48d3eff2787e6029e9))
* **statuscolumn:** ensure Snacks exists when loading before plugin loaded ([97e0e1e](https://github.com/folke/snacks.nvim/commit/97e0e1ec7f1088ee026efdaa789d102461ad49d4))
* **terminal:** properly deal with args in `vim.o.shell`. Fixes [#69](https://github.com/folke/snacks.nvim/issues/69) ([2ccb70f](https://github.com/folke/snacks.nvim/commit/2ccb70fd3a42d188c95db233bfb7469259d56fb6))
* **win:** take border into account for window position ([#64](https://github.com/folke/snacks.nvim/issues/64)) ([f0e47fd](https://github.com/folke/snacks.nvim/commit/f0e47fd5dc3ccc71cdd8866e2ce82749d3797fbb))

## [1.2.0](https://github.com/folke/snacks.nvim/compare/v1.1.0...v1.2.0) (2024-11-11)


### Features

* **bufdelete:** added `wipe` option. Closes [#38](https://github.com/folke/snacks.nvim/issues/38) ([5914cb1](https://github.com/folke/snacks.nvim/commit/5914cb101070956a73462dcb1c81c8462e9e77d7))
* **lazygit:** allow overriding extra lazygit config options ([d2f4f19](https://github.com/folke/snacks.nvim/commit/d2f4f1937e6fa97a48d5839d49f1f3012067bf45))
* **notifier:** added `refresh` option configurable ([df8c9d7](https://github.com/folke/snacks.nvim/commit/df8c9d7724ade9f3c63277f08b237ac3b32b6cfe))
* **notifier:** added backward compatibility for nvim-notify's replace option ([9b9777e](https://github.com/folke/snacks.nvim/commit/9b9777ec3bba97b3ddb37bd824a9ef9a46955582))
* **words:** add `fold_open` and `set_jump_point` config options ([#31](https://github.com/folke/snacks.nvim/issues/31)) ([5dc749b](https://github.com/folke/snacks.nvim/commit/5dc749b045e62e30a156ca8522416a6d1ca9a959))


### Bug Fixes

* added compatibility with Neovim &gt;= 0.9.4 ([4f99818](https://github.com/folke/snacks.nvim/commit/4f99818b0ab98510ab8987a0427afc515fb5f76b))
* **bufdelete:** opts.wipe. See [#38](https://github.com/folke/snacks.nvim/issues/38) ([0efbb93](https://github.com/folke/snacks.nvim/commit/0efbb93e0a4405b955d574746eb57ef6d48ae386))
* **notifier:** take title/footer into account to determine notification width. Fixes [#54](https://github.com/folke/snacks.nvim/issues/54) ([09a6f17](https://github.com/folke/snacks.nvim/commit/09a6f17eccbb551797f522403033f48e63a25f74))
* **notifier:** update layout on vim resize ([7f9f691](https://github.com/folke/snacks.nvim/commit/7f9f691a12d0665146b25a44323f21e18aa46c24))
* **terminal:** `gf` properly opens file ([#45](https://github.com/folke/snacks.nvim/issues/45)) ([340cc27](https://github.com/folke/snacks.nvim/commit/340cc2756e9d7ef0ae9a6f55cdfbfdca7a9defa7))
* **terminal:** pass a list of strings to termopen to prevent splitting. Fixes [#59](https://github.com/folke/snacks.nvim/issues/59) ([458a84b](https://github.com/folke/snacks.nvim/commit/458a84bd1db856c21f234a504ec384191a9899cf))


### Performance Improvements

* **notifier:** only force redraw for new windows and for updated while search is not active. Fixes [#52](https://github.com/folke/snacks.nvim/issues/52) ([da86b1d](https://github.com/folke/snacks.nvim/commit/da86b1deff9a0b1bb66f344241fb07577b6463b8))
* **win:** don't try highlighting snacks internal filetypes ([eb8ab37](https://github.com/folke/snacks.nvim/commit/eb8ab37f6ac421eeda2570257d2279bd12700667))
* **win:** prevent treesitter and syntax attaching to scratch buffers ([cc80f6d](https://github.com/folke/snacks.nvim/commit/cc80f6dc1b7a286cb06c6321bfeb1046f7a59418))

## [1.1.0](https://github.com/folke/snacks.nvim/compare/v1.0.0...v1.1.0) (2024-11-08)


### Features

* **bufdelete:** optional filter and shortcuts to delete `all` and `other` buffers. Closes [#11](https://github.com/folke/snacks.nvim/issues/11) ([71a2346](https://github.com/folke/snacks.nvim/commit/71a234608ffeebfa8a04c652834342fa9ce508c3))
* **debug:** simple log function to quickly log something to a debug.log file ([fc2a8e7](https://github.com/folke/snacks.nvim/commit/fc2a8e74686c7c347ed0aaa5eb607874ecdca288))
* **docs:** docs for highlight groups ([#13](https://github.com/folke/snacks.nvim/issues/13)) ([964cd6a](https://github.com/folke/snacks.nvim/commit/964cd6aa76f3608c7e379b8b1a483ae19f57e279))
* **gitbrowse:** choose to open repo, branch or file. Closes [#10](https://github.com/folke/snacks.nvim/issues/10). Closes [#17](https://github.com/folke/snacks.nvim/issues/17) ([92da87c](https://github.com/folke/snacks.nvim/commit/92da87c910a9b1421e8baae2e67020565526fba8))
* **notifier:** added history to notifier. Closes [#14](https://github.com/folke/snacks.nvim/issues/14) ([65d8c8f](https://github.com/folke/snacks.nvim/commit/65d8c8f00b6589b44410301b790d97c268f86f85))
* **notifier:** added option to show notifs top-down or bottom-up. Closes [#9](https://github.com/folke/snacks.nvim/issues/9) ([080e0d4](https://github.com/folke/snacks.nvim/commit/080e0d403924e1e62d3b88412a41f3ab22594049))
* **notifier:** allow overriding hl groups per notification ([8bcb2bc](https://github.com/folke/snacks.nvim/commit/8bcb2bc805a1785208f96ad7ad96690eee50c925))
* **notifier:** allow setting dynamic options ([36e9f45](https://github.com/folke/snacks.nvim/commit/36e9f45302bc9c200c76349ecd79a319a5944d8c))
* **win:** added default hl groups for windows ([8c0f10b](https://github.com/folke/snacks.nvim/commit/8c0f10b9dade154d355e31aa3f9c8c0ba212205e))
* **win:** allow setting `ft` just for highlighting without actually changing the `filetype` ([cad236f](https://github.com/folke/snacks.nvim/commit/cad236f9bbe46fbb53127014731d8507a3bc80af))
* **win:** disable winblend when colorscheme is transparent. Fixes [#26](https://github.com/folke/snacks.nvim/issues/26) ([12077bc](https://github.com/folke/snacks.nvim/commit/12077bcf65554b585b1f094c69746df402433132))
* **win:** equalize splits ([e982aab](https://github.com/folke/snacks.nvim/commit/e982aabefdf0b1d00ddd850152921e577cd980cc))
* **win:** util methods to handle buffer text ([d3efb92](https://github.com/folke/snacks.nvim/commit/d3efb92aa546eb160782e24e305f74a559eec212))
* **win:** win:focus() ([476fb56](https://github.com/folke/snacks.nvim/commit/476fb56bfd8e32a2805f46fadafbc4eee7878597))
* **words:** `jump` optionally shows notification with reference count ([#23](https://github.com/folke/snacks.nvim/issues/23)) ([6a3f865](https://github.com/folke/snacks.nvim/commit/6a3f865357005c934e2b5ad2cfadaa038775e9e0))
* **words:** configurable mode to show references. Defaults to n, i, c. Closes [#18](https://github.com/folke/snacks.nvim/issues/18) ([d079fbf](https://github.com/folke/snacks.nvim/commit/d079fbfe354ebfec18c4e1bfd7fee695703e3692))


### Bug Fixes

* **config:** deepcopy config where needed ([6c76f91](https://github.com/folke/snacks.nvim/commit/6c76f913981663ec0dba39686018cbc2ff3220b8))
* **config:** fix reading config during setup. Fixes [#2](https://github.com/folke/snacks.nvim/issues/2) ([0d91a4e](https://github.com/folke/snacks.nvim/commit/0d91a4e364866e407901020b59121883cbfb1cf1))
* **notifier:** re-apply winhl since level might have changed with a replace ([b8cc93e](https://github.com/folke/snacks.nvim/commit/b8cc93e273fd481f2b3b7785f64e301d70fd8e45))
* **notifier:** set default conceallevel=2 ([662795c](https://github.com/folke/snacks.nvim/commit/662795c2855b7bfd5e6ec254e469284dacdabb3f))
* **notifier:** try to keep layout when replacing notifs ([9bdb24e](https://github.com/folke/snacks.nvim/commit/9bdb24e735458ea4fd3974939c33ea78cbba0212))
* **terminal:** dont overwrite user opts ([0b08d28](https://github.com/folke/snacks.nvim/commit/0b08d280b605b2e460c1fd92bc87152e66f14430))
* **terminal:** user options ([334895c](https://github.com/folke/snacks.nvim/commit/334895c5bb2ed04f65800abaeb91ccb0487b0f1f))
* **win:** better winfixheight and winfixwidth for splits ([8be14c6](https://github.com/folke/snacks.nvim/commit/8be14c68a7825fff90ca071f0650657ba88da423))
* **win:** disable sidescroloff in minimal style ([107d10b](https://github.com/folke/snacks.nvim/commit/107d10b52e54828606a645517b55802dd807e8ad))
* **win:** dont center float when `relative="cursor"` ([4991e34](https://github.com/folke/snacks.nvim/commit/4991e347dcc6ff6c14443afe9b4d849a67b67944))
* **win:** properly resolve user styles as last ([cc5ee19](https://github.com/folke/snacks.nvim/commit/cc5ee192caf79446d58cbc09487268aa1f86f405))
* **win:** set border to none for backdrop windows ([#19](https://github.com/folke/snacks.nvim/issues/19)) ([f5602e6](https://github.com/folke/snacks.nvim/commit/f5602e60c325f0c60eb6f2869a7222beb88a773c))
* **win:** simpler way to add buffer padding ([f59237f](https://github.com/folke/snacks.nvim/commit/f59237f1dcdceb646bf2552b69b7e2040f80f603))
* **win:** update win/buf opts when needed ([5fd9c42](https://github.com/folke/snacks.nvim/commit/5fd9c426e850c02489943d7177d9e7fddec5e589))
* **words:** disable notify_jump by default ([9576081](https://github.com/folke/snacks.nvim/commit/9576081e871a801f60367e7180543fa41c384755))


### Performance Improvements

* **notifier:** index queue by id ([5df4394](https://github.com/folke/snacks.nvim/commit/5df4394c60958635bf4651d8d7e25f53f48a3965))
* **notifier:** optimize layout code ([8512896](https://github.com/folke/snacks.nvim/commit/8512896228b3e37e3d02c68fa739749c9f0b9838))
* **notifier:** skip processing queue when free space is smaller than min height ([08190a5](https://github.com/folke/snacks.nvim/commit/08190a545857ef09cb6ada4337fe7ec67d3602a9))
* **win:** skip events when setting buf/win options. Trigger FileType on BufEnter only if needed ([61496a3](https://github.com/folke/snacks.nvim/commit/61496a3ef00bd67afb7affcb4933905910a6283c))

## 1.0.0 (2024-11-06)


### Features

* added debug ([6cb43f6](https://github.com/folke/snacks.nvim/commit/6cb43f603360c6fc702b5d7c928dfde22d886e2f))
* added git ([f0a9991](https://github.com/folke/snacks.nvim/commit/f0a999134738c54dccb78ae462774eb228614221))
* added gitbrowse ([a638d8b](https://github.com/folke/snacks.nvim/commit/a638d8bafef85ac6046cfc02e415a8893e0391b9))
* added lazygit ([fc32619](https://github.com/folke/snacks.nvim/commit/fc32619734e4d3c024b8fc2db941c8ac19d2dd6c))
* added notifier ([44011dd](https://github.com/folke/snacks.nvim/commit/44011ddf0da07d0fa89734d21bb770f01a630077))
* added notify ([f4e0130](https://github.com/folke/snacks.nvim/commit/f4e0130ec3cb0299a3a85c589250c114d46f53c2))
* added toggle ([28c3029](https://github.com/folke/snacks.nvim/commit/28c30296991ac5549b49b7ecfb49f108f70d76ba))
* better buffer/window vars for terminal and float ([1abce78](https://github.com/folke/snacks.nvim/commit/1abce78a8b826943d5055464636cd9fad074b4bb))
* bigfile ([8d62b28](https://github.com/folke/snacks.nvim/commit/8d62b285d5026e3d7c064d435c424bab40d1910a))
* **bigfile:** show message when bigfile was detected ([fdc0d3d](https://github.com/folke/snacks.nvim/commit/fdc0d3d1f80a6be64e85a5a25dc34693edadd73f))
* bufdelete ([cc5353f](https://github.com/folke/snacks.nvim/commit/cc5353f6b3f3f3869e2110b2d3d1a95418653213))
* config & setup ([c98c4c0](https://github.com/folke/snacks.nvim/commit/c98c4c030711a59e6791d8e5cab7550e33ac2d2d))
* **config:** get config for snack with defaults and custom opts ([b3d08be](https://github.com/folke/snacks.nvim/commit/b3d08beb8c60fddc6bfbf96ac9f45c4db49e64af))
* **debug:** added simple profile function ([e1f736d](https://github.com/folke/snacks.nvim/commit/e1f736d71fb9020a09019a49d645d4fe6d9f30db))
* **docs:** better handling of overloads ([038b283](https://github.com/folke/snacks.nvim/commit/038b28319c3a4eba7220a679a5759c06e69b8493))
* ensure Snacks global is available when not using setup ([f0458ba](https://github.com/folke/snacks.nvim/commit/f0458bafb059da9885de4fbab1ae5cb6ce2cd0bb))
* float ([d106107](https://github.com/folke/snacks.nvim/commit/d106107cdccc7ecb9931e011a89df6011eed44c4))
* **float:** added support for splits ([977a3d3](https://github.com/folke/snacks.nvim/commit/977a3d345b6da2b819d9bc4870d3d8a7e026728e))
* **float:** better key mappings ([a171a81](https://github.com/folke/snacks.nvim/commit/a171a815b3acd72dd779781df5586a7cd6ddd649))
* initial commit ([63a24f6](https://github.com/folke/snacks.nvim/commit/63a24f6eb047530234297460a9b7ccd6af0b9858))
* **notifier:** add 1 cell left/right padding and make wrapping work properly ([efc9699](https://github.com/folke/snacks.nvim/commit/efc96996e5a98b619e87581e9527c871177dee52))
* **notifier:** added global keep config option ([f32d82d](https://github.com/folke/snacks.nvim/commit/f32d82d1b705512eb56c901d4d7de68eedc827b1))
* **notifier:** added minimal style ([b29a6d5](https://github.com/folke/snacks.nvim/commit/b29a6d5972943cb8fcfdfb94610d850f0ba050b3))
* **notifier:** allow closing notifs with `q` ([97acbbb](https://github.com/folke/snacks.nvim/commit/97acbbb654d13a0d38792fd6383973a2ca01a2bf))
* **notifier:** allow config of default filetype ([8a96888](https://github.com/folke/snacks.nvim/commit/8a968884098be83acb42f31a573e62b63420268e))
* **notifier:** enable wrapping by default ([d02aa2f](https://github.com/folke/snacks.nvim/commit/d02aa2f7cb49273330fd778818124ddb39838372))
* **notifier:** keep notif open when it's the current window ([1e95800](https://github.com/folke/snacks.nvim/commit/1e9580039b706cfc1f526fea2e46b6857473420b))
* quickfile ([d0ce645](https://github.com/folke/snacks.nvim/commit/d0ce6454f95fe056c65324a0f59a250532a658f3))
* rename ([fa33688](https://github.com/folke/snacks.nvim/commit/fa336883019110b8f525081665bf55c19df5f0aa))
* statuscolumn ([99b1700](https://github.com/folke/snacks.nvim/commit/99b170001592fe054368a220599da546de64894e))
* terminal ([e6cc7c9](https://github.com/folke/snacks.nvim/commit/e6cc7c998afa63eaf126d169f4702953f548d39f))
* **terminal:** allow to override the default terminal implementation (like toggleterm) ([11c9ee8](https://github.com/folke/snacks.nvim/commit/11c9ee83aa133f899dad966224df0e2d7de236f2))
* **terminal:** better defaults and winbar ([7ceeb47](https://github.com/folke/snacks.nvim/commit/7ceeb47e545619dff6dd8853f0a368afae7d3ec8))
* **terminal:** better double esc to go to normal mode ([a4af729](https://github.com/folke/snacks.nvim/commit/a4af729b2489714b066ca03f008bf5fe42c93343))
* **win:** better api to deal with sizes ([ac1a50c](https://github.com/folke/snacks.nvim/commit/ac1a50c810c5f67909921592afcebffa566ee3d3))
* **win:** custom views ([12d6f86](https://github.com/folke/snacks.nvim/commit/12d6f863f73cbd3580295adc5bc546c9d10e9e7f))
* words ([73445af](https://github.com/folke/snacks.nvim/commit/73445af400457722508395d18d2c974965c53fe2))


### Bug Fixes

* **config:** don't change defaults in merge ([6e825f5](https://github.com/folke/snacks.nvim/commit/6e825f509ed0e41dbafe5bca0236157772344554))
* **config:** merging of possible nil values ([f5bbb44](https://github.com/folke/snacks.nvim/commit/f5bbb446ed012361bbd54362558d1f32476206c7))
* **debug:** exclude vimrc from callers ([8845a6a](https://github.com/folke/snacks.nvim/commit/8845a6a912a528f63216ffe4b991b025c8955447))
* **float:** don't use backdrop for splits ([5eb64c5](https://github.com/folke/snacks.nvim/commit/5eb64c52aeb7271a3116751f1ec61b00536cdc08))
* **float:** only set default filetype if no ft is set ([66b2525](https://github.com/folke/snacks.nvim/commit/66b252535c7a78f0cd73755fad22b07b814309f1))
* **float:** proper closing of backdrop ([a528e77](https://github.com/folke/snacks.nvim/commit/a528e77397daea422ff84dde64124d4a7e352bc2))
* **notifier:** modifiable ([fd57c24](https://github.com/folke/snacks.nvim/commit/fd57c243015e6f2c863bb6c89e1417e77f3e0ea4))
* **notifier:** modifiable = false ([9ef9e69](https://github.com/folke/snacks.nvim/commit/9ef9e69620fc51d368fcee830a59bc9279594d43))
* **notifier:** show notifier errors with nvim_err_writeln ([e8061bc](https://github.com/folke/snacks.nvim/commit/e8061bcda095e3e9a3110ce697cdf7155e178d6e))
* **notifier:** sorting ([d9a1f23](https://github.com/folke/snacks.nvim/commit/d9a1f23e216230fcb2060e74056778d57d1d7676))
* simplify setup ([787b53e](https://github.com/folke/snacks.nvim/commit/787b53e7635f322bf42a42279f647340daf77770))
* **win:** backdrop ([71dd912](https://github.com/folke/snacks.nvim/commit/71dd912763918fd6b7fd07dd66ccd16afd4fea78))
* **win:** better implementation of window styles (previously views) ([6681097](https://github.com/folke/snacks.nvim/commit/66810971b9bd08e212faae467d884758bf142ffe))
* **win:** dont error when augroup is already deleted ([8c43597](https://github.com/folke/snacks.nvim/commit/8c43597f10dc2916200a8c857026f3f15fd3ae65))
* **win:** dont update win opt noautocmd ([a06e3ed](https://github.com/folke/snacks.nvim/commit/a06e3ed8fcd08aaf43fc2454bb1b0f935053aca7))
* **win:** no need to set EndOfBuffer winhl ([7a7f221](https://github.com/folke/snacks.nvim/commit/7a7f221020e024da831c2a3d72f8a9a0c330d711))
* **win:** use syntax as fallback for treesitter ([f3b69a6](https://github.com/folke/snacks.nvim/commit/f3b69a617a57597571fcf4263463f989fa3b663d))


### Performance Improvements

* **win:** set options with eventignore and handle ft manually ([80d9a89](https://github.com/folke/snacks.nvim/commit/80d9a894f9d6b2087e0dfee6d777e7b78490ba93))
