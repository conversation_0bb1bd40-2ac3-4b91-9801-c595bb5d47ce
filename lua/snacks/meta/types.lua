---@meta _

---@class snacks.plugins
---@field animate snacks.animate
---@field bigfile snacks.bigfile
---@field bufdelete snacks.bufdelete
---@field dashboard snacks.dashboard
---@field debug snacks.debug
---@field dim snacks.dim
---@field explorer snacks.explorer
---@field git snacks.git
---@field gitbrowse snacks.gitbrowse
---@field health snacks.health
---@field image snacks.image
---@field indent snacks.indent
---@field input snacks.input
---@field layout snacks.layout
---@field lazygit snacks.lazygit
---@field meta snacks.meta
---@field notifier snacks.notifier
---@field notify snacks.notify
---@field picker snacks.picker
---@field profiler snacks.profiler
---@field quickfile snacks.quickfile
---@field rename snacks.rename
---@field scope snacks.scope
---@field scratch snacks.scratch
---@field scroll snacks.scroll
---@field statuscolumn snacks.statuscolumn
---@field terminal snacks.terminal
---@field toggle snacks.toggle
---@field util snacks.util
---@field win snacks.win
---@field words snacks.words
---@field zen snacks.zen

---@class snacks.plugins.Config
---@field animate? snacks.animate.Config|{}
---@field bigfile? snacks.bigfile.Config|{}
---@field dashboard? snacks.dashboard.Config|{}
---@field dim? snacks.dim.Config|{}
---@field explorer? snacks.explorer.Config|{}
---@field gitbrowse? snacks.gitbrowse.Config|{}
---@field image? snacks.image.Config|{}
---@field indent? snacks.indent.Config|{}
---@field input? snacks.input.Config|{}
---@field layout? snacks.layout.Config|{}
---@field lazygit? snacks.lazygit.Config|{}
---@field notifier? snacks.notifier.Config|{}
---@field picker? snacks.picker.Config|{}
---@field profiler? snacks.profiler.Config|{}
---@field quickfile? snacks.quickfile.Config|{}
---@field scope? snacks.scope.Config|{}
---@field scratch? snacks.scratch.Config|{}
---@field scroll? snacks.scroll.Config|{}
---@field statuscolumn? snacks.statuscolumn.Config|{}
---@field terminal? snacks.terminal.Config|{}
---@field toggle? snacks.toggle.Config|{}
---@field win? snacks.win.Config|{}
---@field words? snacks.words.Config|{}
---@field zen? snacks.zen.Config|{}
