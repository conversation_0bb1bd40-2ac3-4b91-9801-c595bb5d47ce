---@meta _

---@class snacks.picker
---@field autocmds fun(opts?: snacks.picker.Config|{}): snacks.Picker
---@field buffers fun(opts?: snacks.picker.buffers.Config|{}): snacks.Picker
---@field cliphist fun(opts?: snacks.picker.Config|{}): snacks.Picker
---@field colorschemes fun(opts?: snacks.picker.Config|{}): snacks.Picker
---@field command_history fun(opts?: snacks.picker.history.Config|{}): snacks.Picker
---@field commands fun(opts?: snacks.picker.Config|{}): snacks.Picker
---@field diagnostics fun(opts?: snacks.picker.diagnostics.Config|{}): snacks.Picker
---@field diagnostics_buffer fun(opts?: snacks.picker.diagnostics.Config|{}): snacks.Picker
---@field explorer fun(opts?: snacks.picker.explorer.Config|{}): snacks.Picker
---@field files fun(opts?: snacks.picker.files.Config|{}): snacks.Picker
---@field git_branches fun(opts?: snacks.picker.git.branches.Config|{}): snacks.Picker
---@field git_diff fun(opts?: snacks.picker.git.Config|{}): snacks.Picker
---@field git_files fun(opts?: snacks.picker.git.files.Config|{}): snacks.Picker
---@field git_grep fun(opts?: snacks.picker.git.grep.Config|{}): snacks.Picker
---@field git_log fun(opts?: snacks.picker.git.log.Config|{}): snacks.Picker
---@field git_log_file fun(opts?: snacks.picker.git.log.Config|{}): snacks.Picker
---@field git_log_line fun(opts?: snacks.picker.git.log.Config|{}): snacks.Picker
---@field git_stash fun(opts?: snacks.picker.Config|{}): snacks.Picker
---@field git_status fun(opts?: snacks.picker.git.status.Config|{}): snacks.Picker
---@field grep fun(opts?: snacks.picker.grep.Config|{}): snacks.Picker
---@field grep_buffers fun(opts?: snacks.picker.grep.Config|{}): snacks.Picker
---@field grep_word fun(opts?: snacks.picker.grep.Config|{}): snacks.Picker
---@field help fun(opts?: snacks.picker.help.Config|{}): snacks.Picker
---@field highlights fun(opts?: snacks.picker.Config|{}): snacks.Picker
---@field icons fun(opts?: snacks.picker.icons.Config|{}): snacks.Picker
---@field jumps fun(opts?: snacks.picker.Config|{}): snacks.Picker
---@field keymaps fun(opts?: snacks.picker.keymaps.Config|{}): snacks.Picker
---@field lazy fun(opts?: snacks.picker.Config|{}): snacks.Picker
---@field lines fun(opts?: snacks.picker.lines.Config|{}): snacks.Picker
---@field loclist fun(opts?: snacks.picker.qf.Config|{}): snacks.Picker
---@field lsp_config fun(opts?: snacks.picker.lsp.config.Config|{}): snacks.Picker
---@field lsp_declarations fun(opts?: snacks.picker.lsp.Config|{}): snacks.Picker
---@field lsp_definitions fun(opts?: snacks.picker.lsp.Config|{}): snacks.Picker
---@field lsp_implementations fun(opts?: snacks.picker.lsp.Config|{}): snacks.Picker
---@field lsp_references fun(opts?: snacks.picker.lsp.references.Config|{}): snacks.Picker
---@field lsp_symbols fun(opts?: snacks.picker.lsp.symbols.Config|{}): snacks.Picker
---@field lsp_type_definitions fun(opts?: snacks.picker.lsp.Config|{}): snacks.Picker
---@field lsp_workspace_symbols fun(opts?: snacks.picker.lsp.symbols.Config|{}): snacks.Picker
---@field man fun(opts?: snacks.picker.Config|{}): snacks.Picker
---@field marks fun(opts?: snacks.picker.marks.Config|{}): snacks.Picker
---@field notifications fun(opts?: snacks.picker.notifications.Config|{}): snacks.Picker
---@field picker_actions fun(opts?: snacks.picker.Config|{}): snacks.Picker
---@field picker_format fun(opts?: snacks.picker.Config|{}): snacks.Picker
---@field picker_layouts fun(opts?: snacks.picker.Config|{}): snacks.Picker
---@field picker_preview fun(opts?: snacks.picker.Config|{}): snacks.Picker
---@field pickers fun(opts?: snacks.picker.Config|{}): snacks.Picker
---@field projects fun(opts?: snacks.picker.projects.Config|{}): snacks.Picker
---@field qflist fun(opts?: snacks.picker.qf.Config|{}): snacks.Picker
---@field recent fun(opts?: snacks.picker.recent.Config|{}): snacks.Picker
---@field registers fun(opts?: snacks.picker.Config|{}): snacks.Picker
---@field resume fun(): snacks.Picker
---@field search_history fun(opts?: snacks.picker.history.Config|{}): snacks.Picker
---@field smart fun(opts?: snacks.picker.smart.Config|{}): snacks.Picker
---@field spelling fun(opts?: snacks.picker.Config|{}): snacks.Picker
---@field treesitter fun(opts?: snacks.picker.treesitter.Config|{}): snacks.Picker
---@field undo fun(opts?: snacks.picker.undo.Config|{}): snacks.Picker
---@field zoxide fun(opts?: snacks.picker.Config|{}): snacks.Picker
